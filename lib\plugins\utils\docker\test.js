ig.module('plugins.utils.docker.test')
.requires(
    'plugins.utils.docker.docker',
    'plugins.utils.docker.examples'
)
.defines(function () {
    
    /**
     * Docker Plugin Test Suite
     * 
     * This file provides test functions to verify the docker plugin functionality.
     * Call ig.Docker.runTests() to execute all tests.
     */
    
    ig.Docker.runTests = function() {
        console.log('=== Docker Plugin Test Suite ===');
        
        // Test 1: Basic docking functionality
        this.testBasicDocking();
        
        // Test 2: Configuration validation
        this.testConfigValidation();
        
        // Test 3: Manager functionality
        this.testManagerFunctionality();
        
        // Test 4: Edge cases
        this.testEdgeCases();

        // Test 5: Responsive mode
        this.testResponsiveMode();

        console.log('=== Docker Plugin Tests Complete ===');
    };
    
    ig.Docker.testBasicDocking = function() {
        console.log('Test 1: Basic Docking Functionality');
        
        try {
            // Create parent entity
            var parent = ig.game.spawnEntity(ig.Entity, 100, 100, {
                size: { x: 50, y: 50 }
            });
            
            // Create child entity
            var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                size: { x: 10, y: 10 }
            });
            
            // Test dockTo method
            var result = child.dockTo(parent, {
                percent: { x: 0.5, y: 0.5 },
                offset: { x: 5, y: 5 }
            });
            
            if (result && child.isDocked()) {
                console.log('✓ Basic docking successful');
                
                // Verify position calculation
                var expectedX = parent.pos.x + (parent.size.x * 0.5) + 5;
                var expectedY = parent.pos.y + (parent.size.y * 0.5) + 5;
                
                if (Math.abs(child.pos.x - expectedX) < 0.1 && Math.abs(child.pos.y - expectedY) < 0.1) {
                    console.log('✓ Position calculation correct');
                } else {
                    console.log('✗ Position calculation incorrect');
                    console.log('Expected:', expectedX, expectedY, 'Got:', child.pos.x, child.pos.y);
                }
            } else {
                console.log('✗ Basic docking failed');
            }
            
            // Test undocking
            child.undock();
            if (!child.isDocked()) {
                console.log('✓ Undocking successful');
            } else {
                console.log('✗ Undocking failed');
            }
            
            // Clean up
            parent.kill();
            child.kill();
            
        } catch (error) {
            console.log('✗ Basic docking test failed with error:', error);
        }
    };
    
    ig.Docker.testConfigValidation = function() {
        console.log('Test 2: Configuration Validation');
        
        try {
            var parent = ig.game.spawnEntity(ig.Entity, 100, 100, {
                size: { x: 50, y: 50 }
            });
            
            var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                size: { x: 10, y: 10 }
            });
            
            // Test invalid configurations
            var invalidConfigs = [
                null,
                undefined,
                { percent: { x: -1, y: 2 } }, // Out of range
                { percent: { x: 'invalid', y: 0.5 } }, // Wrong type
                { offset: { x: 'invalid', y: 0 } } // Wrong type
            ];
            
            var validConfigsProcessed = 0;
            
            for (var i = 0; i < invalidConfigs.length; i++) {
                var result = child.dockTo(parent, invalidConfigs[i]);
                if (result) {
                    validConfigsProcessed++;
                    child.undock(); // Clean up for next test
                }
            }
            
            if (validConfigsProcessed === invalidConfigs.length) {
                console.log('✓ Configuration validation working (invalid configs normalized)');
            } else {
                console.log('✗ Some invalid configurations were rejected');
            }
            
            // Test valid configuration
            var validResult = child.dockTo(parent, {
                percent: { x: 0.25, y: 0.75 },
                offset: { x: -10, y: 15 }
            });
            
            if (validResult) {
                console.log('✓ Valid configuration accepted');
            } else {
                console.log('✗ Valid configuration rejected');
            }
            
            // Clean up
            parent.kill();
            child.kill();
            
        } catch (error) {
            console.log('✗ Configuration validation test failed with error:', error);
        }
    };
    
    ig.Docker.testManagerFunctionality = function() {
        console.log('Test 3: Manager Functionality');
        
        try {
            var initialCount = ig.DockerManager.getDockedEntityCount();
            
            // Create multiple docked entities
            var parent = ig.game.spawnEntity(ig.Entity, 100, 100, {
                size: { x: 50, y: 50 }
            });
            
            var children = [];
            for (var i = 0; i < 3; i++) {
                var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                    size: { x: 10, y: 10 }
                });
                child.dockTo(parent, {
                    percent: { x: i * 0.5, y: 0.5 },
                    offset: { x: 0, y: 0 }
                });
                children.push(child);
            }
            
            // Test manager count
            var newCount = ig.DockerManager.getDockedEntityCount();
            if (newCount === initialCount + 3) {
                console.log('✓ Manager tracking docked entities correctly');
            } else {
                console.log('✗ Manager count incorrect. Expected:', initialCount + 3, 'Got:', newCount);
            }
            
            // Test getEntitiesDockedTo
            var dockedToParent = ig.DockerManager.getEntitiesDockedTo(parent);
            if (dockedToParent.length === 3) {
                console.log('✓ Manager finding docked entities correctly');
            } else {
                console.log('✗ Manager finding incorrect number of docked entities');
            }
            
            // Test undockAllFrom
            ig.DockerManager.undockAllFrom(parent);
            var afterUndockCount = ig.DockerManager.getDockedEntityCount();
            if (afterUndockCount === initialCount) {
                console.log('✓ Manager undockAllFrom working correctly');
            } else {
                console.log('✗ Manager undockAllFrom failed');
            }
            
            // Clean up
            parent.kill();
            for (var j = 0; j < children.length; j++) {
                children[j].kill();
            }
            
        } catch (error) {
            console.log('✗ Manager functionality test failed with error:', error);
        }
    };
    
    ig.Docker.testEdgeCases = function() {
        console.log('Test 4: Edge Cases');
        
        try {
            // Test docking to null parent
            var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                size: { x: 10, y: 10 }
            });
            
            var nullResult = child.dockTo(null, { percent: { x: 0.5, y: 0.5 } });
            if (!nullResult) {
                console.log('✓ Null parent handling correct');
            } else {
                console.log('✗ Null parent should be rejected');
            }
            
            // Test parent without size
            var invalidParent = ig.game.spawnEntity(ig.Entity, 100, 100);
            delete invalidParent.size;
            
            var invalidSizeResult = child.dockTo(invalidParent, { percent: { x: 0.5, y: 0.5 } });
            if (!invalidSizeResult) {
                console.log('✓ Parent without size handling correct');
            } else {
                console.log('✗ Parent without size should be rejected');
            }
            
            // Test double docking
            var parent1 = ig.game.spawnEntity(ig.Entity, 100, 100, { size: { x: 50, y: 50 } });
            var parent2 = ig.game.spawnEntity(ig.Entity, 200, 200, { size: { x: 50, y: 50 } });
            
            child.dockTo(parent1, { percent: { x: 0.5, y: 0.5 } });
            var firstParent = child.getDockerParent();
            
            child.dockTo(parent2, { percent: { x: 0.5, y: 0.5 } });
            var secondParent = child.getDockerParent();
            
            if (firstParent === parent1 && secondParent === parent2) {
                console.log('✓ Double docking (re-docking) working correctly');
            } else {
                console.log('✗ Double docking failed');
            }
            
            // Clean up
            child.kill();
            parent1.kill();
            parent2.kill();
            invalidParent.kill();
            
        } catch (error) {
            console.log('✗ Edge cases test failed with error:', error);
        }
    };

    ig.Docker.testResponsiveMode = function() {
        console.log('Test 5: Responsive Mode');

        try {
            var parent = ig.game.spawnEntity(ig.Entity, 100, 100, {
                size: { x: 50, y: 50 }
            });

            var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                size: { x: 10, y: 10 }
            });

            // Test non-responsive mode (default)
            child.dockTo(parent, {
                percent: { x: 0.5, y: 0.5 },
                offset: { x: 10, y: 10 },
                isResponsive: false
            });

            var expectedX = parent.pos.x + (parent.size.x * 0.5) + 10;
            var expectedY = parent.pos.y + (parent.size.y * 0.5) + 10;

            if (Math.abs(child.pos.x - expectedX) < 0.1 && Math.abs(child.pos.y - expectedY) < 0.1) {
                console.log('✓ Non-responsive mode working correctly');
            } else {
                console.log('✗ Non-responsive mode failed');
            }

            // Test responsive mode
            child.updateDockerConfig({
                percent: { x: 0.5, y: 0.5 },
                offset: { x: 10, y: 10 },
                isResponsive: true
            });

            var expectedXResponsive = expectedX - (ig.game.screen ? ig.game.screen.x : 0);
            var expectedYResponsive = expectedY - (ig.game.screen ? ig.game.screen.y : 0);

            if (Math.abs(child.pos.x - expectedXResponsive) < 0.1 && Math.abs(child.pos.y - expectedYResponsive) < 0.1) {
                console.log('✓ Responsive mode working correctly');
            } else {
                console.log('✗ Responsive mode failed');
                console.log('Expected:', expectedXResponsive, expectedYResponsive, 'Got:', child.pos.x, child.pos.y);
            }

            // Test config retrieval includes isResponsive
            var config = child.getDockerConfig();
            if (config && typeof config.isResponsive === 'boolean' && config.isResponsive === true) {
                console.log('✓ Responsive config retrieval working');
            } else {
                console.log('✗ Responsive config retrieval failed');
            }

            // Clean up
            parent.kill();
            child.kill();

        } catch (error) {
            console.log('✗ Responsive mode test failed with error:', error);
        }
    };
    
    // Test spawning with dockerObject
    ig.Docker.testSpawnWithDockerObject = function() {
        console.log('Test 5: Spawn with dockerObject');
        
        try {
            var parent = ig.game.spawnEntity(ig.Entity, 100, 100, {
                size: { x: 50, y: 50 }
            });
            
            // Test Method 2: spawning with dockerObject
            var child = ig.game.spawnEntity(ig.Entity, 0, 0, {
                size: { x: 10, y: 10 },
                dockerObject: parent,
                percent: { x: 0.25, y: 0.75 },
                offset: { x: 5, y: -5 }
            });
            
            // Give it a frame to process the deferred docking
            setTimeout(function() {
                if (child.isDocked() && child.getDockerParent() === parent) {
                    console.log('✓ Spawn with dockerObject successful');
                } else {
                    console.log('✗ Spawn with dockerObject failed');
                }
                
                // Clean up
                parent.kill();
                child.kill();
            }, 10);
            
        } catch (error) {
            console.log('✗ Spawn with dockerObject test failed with error:', error);
        }
    };
    
    // Create a visual test scene
    ig.Docker.createTestScene = function() {
        console.log('Creating Docker Test Scene...');
        
        // Clear existing entities
        for (var i = ig.game.entities.length - 1; i >= 0; i--) {
            var entity = ig.game.entities[i];
            if (entity.constructor !== EntityDemoControl) {
                entity.kill();
            }
        }
        
        // Create test entities
        var demoScene = ig.Docker.createDemoScene();
        
        // Run automated tests
        this.runTests();
        this.testSpawnWithDockerObject();
        
        return demoScene;
    };
});
