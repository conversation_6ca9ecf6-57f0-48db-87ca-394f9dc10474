ig.module('game.game-data')
.requires(
    'impact.entity'
)
.defines(function () {
    "use strict";

    ig.GameData = ig.global.GameData = {
        "isCheat": false,

        "imageDefPath": "media/graphics/sprites/game",

        "fastAnim": false,

        "typeLeaves": [1, 1, 2, 4, 3, 2, 4, 1, 2, 2, 2, 1, 1, 3, 2, 2, 3, 2, 3, 3, 4, 3, 1, 1, 1, 3, 3, 3, 2, 3, 4, 1, 2, 4, 1, 1, 2, 1, 2, 4, 3, 1, 4, 2, 4, 4, 4, 2, 1, 4, 2, 2, 4, 2, 2, 1, 2, 1, 1, 4, 3, 1, 4, 2, 1, 4, 3, 1, 2, 1, 4, 2, 4, 4, 1, 3, 3, 4, 1, 1, 1, 1, 4, 3, 1, 4, 1, 1, 1, 1, 2, 1, 1, 1, 4, 4, 4, 1, 2, 1, 3, 4, 1, 3, 1, 4, 3, 1, 1, 3, 2, 1, 3, 4, 2, 2, 2, 1, 2, 1, 2, 2, 1, 2, 1, 2, 4, 3, 4, 1, 4, 3, 3, 1, 2, 1, 3, 1, 3, 3, 3, 2, 4, 3, 4, 2, 2, 2, 3, 2, 3, 2, 4, 4, 4, 2, 1, 1, 1, 2, 1, 3, 2, 4, 3, 4, 4, 2, 3, 1, 1, 3, 4, 4, 2, 1, 1, 1, 2, 2, 4, 4, 4, 1, 3, 3, 4, 1, 4, 4, 4, 2, 2, 1, 1, 3, 3, 2, 2, 2, 2, 2, 3, 3, 3, 2, 3, 1, 4, 3, 4, 1, 1, 4, 2, 2, 3, 2, 1, 3, 4, 1, 4, 3, 1, 2, 3, 1, 4, 1, 4, 2, 2, 2, 4, 1, 4, 1, 1, 4, 3, 3, 2, 2, 4, 1, 2, 2, 1, 2, 1, 2, 2, 4, 4, 4, 3, 3, 3, 3, 2, 4, 2, 2, 3, 2, 3, 3, 4, 3, 3, 1, 2, 4, 2, 2, 3, 3, 3, 3, 4, 4, 1, 4, 3, 4, 4, 3, 1, 2, 1, 2, 1, 1, 2, 3, 2, 3, 1, 4, 2, 2, 3, 1, 1, 2, 4, 2, 3, 1, 4, 1, 1, 2, 4, 4, 1, 3, 1, 3, 4, 4, 4, 3, 2, 1, 3, 2, 4, 1, 2, 1, 1, 1, 2, 4, 1, 3, 1, 4, 1, 3, 4, 1, 1, 2, 1, 3, 2, 3, 4, 2, 2, 3, 3, 3, 4, 4, 2, 3, 2, 1, 4, 3, 3, 3, 3, 4, 2, 1, 1, 1, 3, 3, 4, 3, 2, 3, 4, 4, 1, 3, 2, 4, 1, 2, 2, 3, 1, 1, 1, 1, 1, 1, 2, 2, 3, 2, 4, 3, 4, 1, 4, 2, 4, 2, 3, 4, 3, 3, 1, 4, 2, 2, 1, 3, 1, 3, 4, 3, 4, 2, 1, 1, 1, 3, 1, 4, 1, 3, 1, 3, 3, 2, 2, 2, 3, 4, 4, 4, 4, 4, 1, 3, 2, 2, 3, 3, 4, 4, 4, 1, 2, 4, 1, 1, 1, 2, 3, 2, 4, 1, 3, 3, 3, 4, 4, 1, 2, 4, 4, 3, 4, 3, 4, 3, 3, 3, 4, 4, 1, 1, 4, 2, 3, 1, 1, 3, 4, 2, 2, 2, 3, 2, 2, 4, 2, 1, 4, 4, 2],
        // "typeLeaves":[1,4,2,4,3,3,1,4,1,4,2,3,3,3,4,2,2,1,2,4,1,4,3,1,1,1,1,3,1,3,2,4,3,2,3,4,2,3,2,1,2,2,2,2,2,3,3,1,3,4,1,2,2,1,1,4,2,1,4,3,2,1,3,2,2,2,1,3,3,2,4,2,4,4,4,4,3,1,4,3,4,1,4,2,1,1,3,2,4,1,2,1,4,2,1,2,2,3,3,1,1,3,1,3,3,2,2,3,3,3,2,1,1,1,4,4,4,2,3,3,3,3,2,1,2,3,1,4,3,1,1,1,4,2,3,4,1,3,1,3,4,4,3,1,3,2,4,3,4,1,1,4,3,2,1,4,3,4,1,3,1,1,3,1,4,1,1,3,3,2,4,2,2,4,4,3,3,2,3,2,3,4,4,2,3,1,1,3,3,1,2,4,2,2,4,3,1,3,3,3,3,4,3,4,4,2,4,4,2,2,1,3,2,2,2,1,2,1,4,1,2,1,2,3,3,3,4,2,3,4,4,1,1,3,3,4,3,3,2,2,1,2,1,4,4,1,1,1,2,4,4,3,2,4,4,4,3,1,4,2,3,2,2,3,1,1,4,1,4,4,3,3,1,4,3,3,2,3,1,4,4,3,4,4,1,2,2,1,4,3,2,3,1,3,4,3,1,3,3,1,3,1,4,4,3,1,1,2,4,1,4,4,3,2,2,2,4,1,2,1,4,2,4,4,1,2,3,4,1,1,1,1,3,2,3,3,3,4,4,4,4,1,4,1,3,1,1,1,1,1,4,2,3,3,4,2,3,1,3,2,4,4,4,4,2,3,2,1,1,3,3,4,4,4,4,3,2,1,1,4,1,1,2,3,2,3,2,4,3,3,4,1,2,4,2,1,2,2,2,4,3,4,1,1,4,4,1,3,3,2,1,3,2,3,2,4,3,4,1,1,2,1,3,1,3,4,2,2,4,4,1,2,3,3,2,1,1,3,4,4,2,2,3,1,4,1,3,2,1,3,4,4,4,3,1,3,4,3,4,4,2,4,4,2,4,4,1,1,1,4,1,3,1,1,1,3,4,4,1,2,1,1,4,3,2,4,2,2,4,1,4,4,1,3,3,4,1,1,1,4,1],

        "flipLeaves": [1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, -1, -1, -1, -1, -1, -1, -1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, -1, -1, 1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, 1, 1, -1, 1, -1, 1, 1, -1, 1, 1, -1, -1, 1, 1, 1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 1, 1, -1, -1, 1, 1, -1, 1, 1, 1, 1, -1, -1, 1, -1, 1, 1, 1, -1, -1, -1, 1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, 1, 1, 1, -1, -1, 1, 1, -1, 1, 1, 1, 1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, 1, 1, 1, -1, 1, 1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 1, 1, -1, 1, 1, 1, 1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, 1, -1, -1, 1, -1, 1, 1, 1, -1, 1, 1, 1, 1, 1, -1, -1, 1, 1, 1, 1, 1, -1, 1, 1, 1, -1, -1, -1, -1, -1, -1, -1, 1, 1, -1, -1, -1, 1, 1, -1, 1, 1, -1, -1, 1, 1, -1, -1, 1, -1, 1, 1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, 1, 1, 1, 1, -1, 1, -1, -1, -1, 1, 1, -1, 1, 1, 1, -1, 1, -1, -1, 1, 1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 1, -1, -1, 1, 1, -1, 1, 1, 1, -1, -1, 1, -1, 1, 1, -1, 1, 1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, 1, -1, 1, -1, 1, 1, -1, 1, -1, -1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 1, 1, -1, -1, -1, 1, 1, 1, 1, 1, -1, 1, 1, 1, 1, 1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, 1, 1, -1, 1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, -1, -1, -1, 1, 1, 1, -1, -1, 1, 1, -1, 1, -1, 1, 1, -1, -1, -1, -1, 1, 1, 1, -1, 1, -1, -1, -1, 1, 1, -1, -1, -1, 1, 1, 1, -1, 1, 1, -1, -1],

        "posLeaves": [
            { x: 0, y: 0, angle: 0 },
            //100
            { x: 647, y: 423, angle: 9 }, { x: 21, y: 760, angle: -43 }, { x: 749, y: 772, angle: 38 }, { x: 323, y: 112, angle: -24 }, { x: 424, y: 8, angle: -61 }, { x: 63, y: 220, angle: 32 }, { x: 276, y: 341, angle: 0 }, { x: 249, y: 601, angle: 5 }, { x: 536, y: 813, angle: 0 }, { x: 130, y: 910, angle: -100 }, { x: 445, y: 459, angle: 0 }, { x: 733, y: 910, angle: 104 }, { x: 552, y: 157, angle: 49 }, { x: 39, y: 493, angle: -30 }, { x: 706, y: 498, angle: 10 }, { x: 263, y: 702, angle: -22 }, { x: 578, y: 648, angle: 36 }, { x: 271, y: 895, angle: 11 }, { x: 565, y: 284, angle: 0 }, { x: 78, y: 541, angle: 14 }, { x: 451, y: 917, angle: 49 }, { x: 251, y: 163, angle: 13 }, { x: 251, y: 428, angle: -38 }, { x: 461, y: 106, angle: 49 }, { x: 117, y: 291, angle: -24 }, { x: 191, y: 899, angle: 8 }, { x: 712, y: 679, angle: 49 }, { x: 337, y: 376, angle: 11 }, { x: 461, y: 601, angle: 0 }, { x: 388, y: 147, angle: 0 }, { x: 601, y: 310, angle: 0 }, { x: 154, y: 486, angle: -50 }, { x: 655, y: 669, angle: 20 }, { x: 72, y: 641, angle: -4 }, { x: 361, y: 181, angle: 28 }, { x: 634, y: 955, angle: 130 }, { x: 154, y: 648, angle: -13 }, { x: 591, y: 493, angle: 144 }, { x: 216, y: 248, angle: 0 }, { x: 690, y: 732, angle: 0 }, { x: 257, y: 942, angle: -16 }, { x: 390, y: 443, angle: 0 }, { x: 517, y: 955, angle: -28 }, { x: 473, y: 248, angle: -9 }, { x: 374, y: 665, angle: 0 }, { x: 636, y: 512, angle: -15 }, { x: 106, y: 335, angle: 7 }, { x: 184, y: 695, angle: 26 }, { x: 351, y: 890, angle: 19 }, { x: 540, y: 683, angle: -28 }, { x: 121, y: 783, angle: -113 }, { x: 380, y: 241, angle: 20 }, { x: 572, y: 552, angle: -31 }, { x: 314, y: 723, angle: 12 }, { x: 536, y: 357, angle: 0 }, { x: 289, y: 497, angle: 27 }, { x: 374, y: 940, angle: -26 }, { x: 456, y: 988, angle: -29 }, { x: 522, y: 471, angle: 29 }, { x: 263, y: 988, angle: -118 }, { x: 449, y: 328, angle: 49 }, { x: 168, y: 377, angle: 11 }, { x: 461, y: 641, angle: 0 }, { x: 216, y: 802, angle: 8 }, { x: 460, y: 772, angle: 0 }, { x: 351, y: 679, angle: 125 }, { x: 393, y: 508, angle: -50 }, { x: 469, y: 463, angle: 31 }, { x: 403, y: 1025, angle: -11 }, { x: 327, y: 1010, angle: 10 }, { x: 305, y: 791, angle: 47 }, { x: 345, y: 263, angle: -18 }, { x: 599, y: 688, angle: 129 }, { x: 509, y: 552, angle: 0 }, { x: 201, y: 512, angle: -110 }, { x: 389, y: 323, angle: 16 }, { x: 636, y: 806, angle: -9 }, { x: 210, y: 927, angle: -102 }, { x: 609, y: 865, angle: -14 }, { x: 300, y: 397, angle: 0 }, { x: 673, y: 512, angle: 118 }, { x: 228, y: 699, angle: 33 }, { x: 618, y: 523, angle: 135 }, { x: 5, y: 591, angle: -39 }, { x: 753, y: 822, angle: 128 }, { x: 422, y: 41, angle: 61 }, { x: 277, y: 470, angle: -71 }, { x: 688, y: 871, angle: 41 }, { x: 46, y: 696, angle: -29 }, { x: 641, y: 895, angle: 90 }, { x: 652, y: 449, angle: 136 }, { x: 548, y: 186, angle: 100 }, { x: 101, y: 757, angle: -51 }, { x: 423, y: 80, angle: -30 }, { x: 564, y: 680, angle: 140 }, { x: 79, y: 315, angle: -59 }, { x: 524, y: 382, angle: 84 }, { x: 471, y: 139, angle: 98 }, { x: 47, y: 234, angle: 0 }, { x: 503, y: 974, angle: 128 },
            //200
            { x: 506, y: 717, angle: -10 }, { x: 405, y: 1040, angle: 128 }, { x: 159, y: 774, angle: 0 }, { x: 331, y: 1022, angle: -106 }, { x: 718, y: 493, angle: 91 }, { x: 242, y: 366, angle: -21 }, { x: 584, y: 460, angle: 25 }, { x: 300, y: 611, angle: 35 }, { x: 531, y: 860, angle: -30 }, { x: 252, y: 833, angle: 0 }, { x: 578, y: 931, angle: -12 }, { x: 271, y: 212, angle: 26 }, { x: 157, y: 381, angle: -70 }, { x: 419, y: 513, angle: 80 }, { x: 232, y: 524, angle: 8 }, { x: 413, y: 802, angle: -35 }, { x: 675, y: 759, angle: 71 }, { x: 440, y: 115, angle: 15 }, { x: 390, y: 553, angle: -42 }, { x: 652, y: 790, angle: 78 }, { x: 507, y: 192, angle: 136 }, { x: 181, y: 798, angle: -117 }, { x: 434, y: 360, angle: 72 }, { x: 283, y: 964, angle: 9 }, { x: 530, y: 547, angle: 135 }, { x: 185, y: 454, angle: 36 }, { x: 447, y: 297, angle: -19 }, { x: 430, y: 860, angle: -32 }, { x: 604, y: 306, angle: 84 }, { x: 441, y: 1004, angle: 102 }, { x: 361, y: 275, angle: -77 }, { x: 288, y: 1002, angle: -24 }, { x: 559, y: 947, angle: 124 }, { x: 426, y: 364, angle: -47 }, { x: 440, y: 138, angle: 57 }, { x: 174, y: 415, angle: -5 }, { x: 117, y: 681, angle: 12 }, { x: 559, y: 687, angle: 17 }, { x: 570, y: 321, angle: 106 }, { x: 698, y: 928, angle: 120 }, { x: 645, y: 459, angle: 45 }, { x: 254, y: 249, angle: -106 }, { x: 147, y: 923, angle: 0 }, { x: 167, y: 212, angle: 28 }, { x: 633, y: 665, angle: 0 }, { x: 498, y: 746, angle: 82 }, { x: 152, y: 266, angle: 0 }, { x: 422, y: 691, angle: 35 }, { x: 429, y: 361, angle: -16 }, { x: 594, y: 903, angle: 65 }, { x: 293, y: 764, angle: -28 }, { x: 414, y: 500, angle: -1 }, { x: 566, y: 466, angle: 132 }, { x: 230, y: 169, angle: -32 }, { x: 288, y: 531, angle: -37 }, { x: 360, y: 1033, angle: -135 }, { x: 332, y: 179, angle: 9 }, { x: 183, y: 467, angle: -35 }, { x: 453, y: 812, angle: 61 }, { x: 450, y: 299, angle: 45 }, { x: 182, y: 609, angle: 41 }, { x: 318, y: 857, angle: 24 }, { x: 637, y: 931, angle: -55 }, { x: 351, y: 413, angle: -118 }, { x: 439, y: 147, angle: -51 }, { x: 111, y: 727, angle: -35 }, { x: 476, y: 982, angle: 0 }, { x: 397, y: 570, angle: 38 }, { x: 305, y: 641, angle: -57 }, { x: 428, y: 183, angle: 41 }, { x: 406, y: 916, angle: -9 }, { x: 135, y: 764, angle: -36 }, { x: 526, y: 384, angle: -57 }, { x: 440, y: 997, angle: -179 }, { x: 219, y: 515, angle: -48 }, { x: 254, y: 836, angle: -94 }, { x: 575, y: 540, angle: 114 }, { x: 745, y: 777, angle: 86 }, { x: 358, y: 417, angle: -27 }, { x: 369, y: 982, angle: -20 }, { x: 292, y: 548, angle: 35 }, { x: 162, y: 791, angle: -53 }, { x: 593, y: 467, angle: -32 }, { x: 302, y: 406, angle: -77 }, { x: 281, y: 246, angle: -30 }, { x: 457, y: 470, angle: 94 }, { x: 533, y: 969, angle: 22 }, { x: 399, y: 635, angle: 137 }, { x: 339, y: 1031, angle: -16 }, { x: 122, y: 332, angle: -95 }, { x: 380, y: 429, angle: -72 }, { x: 622, y: 687, angle: 57 }, { x: 429, y: 868, angle: 65 }, { x: 62, y: 266, angle: 15 }, { x: 406, y: 635, angle: 30 }, { x: 427, y: 98, angle: -61 }, { x: 206, y: 813, angle: -34 }, { x: 635, y: 920, angle: 111 }, { x: 576, y: 459, angle: -146 }, { x: 437, y: 225, angle: -26 },
            //300
            { x: 31, y: 599, angle: 7 }, { x: 232, y: 969, angle: -90 }, { x: 637, y: 843, angle: 33 }, { x: 556, y: 330, angle: 88 }, { x: 64, y: 767, angle: -100 }, { x: 572, y: 690, angle: 56 }, { x: 105, y: 583, angle: 23 }, { x: 136, y: 279, angle: 64 }, { x: 496, y: 558, angle: 62 }, { x: 105, y: 605, angle: -16 }, { x: 572, y: 295, angle: -25 }, { x: 340, y: 686, angle: 57 }, { x: 716, y: 909, angle: 46 }, { x: 300, y: 775, angle: 18 }, { x: 420, y: 414, angle: -10 }, { x: 63, y: 502, angle: -90 }, { x: 488, y: 767, angle: 6 }, { x: 351, y: 887, angle: -76 }, { x: 31, y: 619, angle: -73 }, { x: 447, y: 809, angle: -5 }, { x: 386, y: 1067, angle: -18 }, { x: 519, y: 412, angle: 0 }, { x: 62, y: 661, angle: 0 }, { x: 553, y: 829, angle: 42 }, { x: 499, y: 215, angle: 57 }, { x: 422, y: 55, angle: 13 }, { x: 406, y: 949, angle: -6 }, { x: 389, y: 576, angle: -17 }, { x: 478, y: 984, angle: 55 }, { x: 304, y: 649, angle: 38 }, { x: 402, y: 1029, angle: -165 }, { x: 241, y: 531, angle: -85 }, { x: 410, y: 1022, angle: 27 }, { x: 447, y: 486, angle: 24 }, { x: 412, y: 414, angle: 30 }, { x: 70, y: 294, angle: -19 }, { x: 241, y: 183, angle: -3 }, { x: 188, y: 625, angle: -52 }, { x: 663, y: 780, angle: 0 }, { x: 365, y: 286, angle: 0 }, { x: 235, y: 720, angle: -62 }, { x: 528, y: 877, angle: 47 }, { x: 129, y: 346, angle: 33 }, { x: 543, y: 157, angle: 104 }, { x: 497, y: 558, angle: 139 }, { x: 674, y: 409, angle: 57 }, { x: 434, y: 1000, angle: 71 }, { x: 299, y: 817, angle: 0 }, { x: 718, y: 492, angle: 39 }, { x: 296, y: 620, angle: 0 }, { x: 734, y: 795, angle: 0 }, { x: 295, y: 248, angle: -99 }, { x: 630, y: 510, angle: 104 }, { x: 224, y: 967, angle: -5 }, { x: 708, y: 682, angle: 100 }, { x: 122, y: 698, angle: 72 }, { x: 321, y: 105, angle: 36 }, { x: 685, y: 665, angle: 32 }, { x: 256, y: 837, angle: -55 }, { x: 398, y: 543, angle: 12 }, { x: 663, y: 552, angle: 125 }, { x: 435, y: 895, angle: 0 }, { x: 365, y: 276, angle: -105 }, { x: 133, y: 906, angle: -153 }, { x: 547, y: 657, angle: -175 }, { x: 54, y: 497, angle: 0 }, { x: 449, y: 318, angle: 9 }, { x: 321, y: 704, angle: 100 }, { x: 96, y: 482, angle: 5 }, { x: 631, y: 854, angle: 0 }, { x: 409, y: 633, angle: -10 }, { x: 316, y: 1019, angle: -73 }, { x: 360, y: 221, angle: -24 }, { x: 322, y: 869, angle: -36 }, { x: 572, y: 941, angle: 86 }, { x: 123, y: 517, angle: 25 }, { x: 697, y: 818, angle: -131 }, { x: 278, y: 402, angle: -8 }, { x: 308, y: 624, angle: -155 }, { x: 736, y: 796, angle: 100 }, { x: 416, y: 748, angle: 9 }, { x: 536, y: 375, angle: 51 }, { x: 278, y: 478, angle: 0 }, { x: 398, y: 1027, angle: 70 }, { x: 72, y: 661, angle: 25 }, { x: 432, y: 135, angle: 0 }, { x: 540, y: 469, angle: 82 }, { x: 478, y: 639, angle: 44 }, { x: 254, y: 370, angle: -90 }, { x: 638, y: 460, angle: -6 }, { x: 485, y: 243, angle: 24 }, { x: 236, y: 732, angle: 0 }, { x: 436, y: 834, angle: 32 }, { x: 385, y: 465, angle: 0 }, { x: 546, y: 726, angle: 122 }, { x: 96, y: 766, angle: -59 }, { x: 153, y: 370, angle: -116 }, { x: 136, y: 911, angle: -39 }, { x: 384, y: 59, angle: -33 }, { x: 733, y: 637, angle: 25 },
            //400
            { x: 565, y: 327, angle: 0 }, { x: 523, y: 915, angle: 0 }, { x: 197, y: 920, angle: -23 }, { x: 528, y: 953, angle: 122 }, { x: 252, y: 350, angle: 0 }, { x: 82, y: 320, angle: -95 }, { x: 428, y: 365, angle: 26 }, { x: 182, y: 620, angle: -8 }, { x: 532, y: 658, angle: 113 }, { x: 123, y: 721, angle: 0 }, { x: 446, y: 262, angle: -8 }, { x: 234, y: 534, angle: 0 }, { x: 422, y: 810, angle: 0 }, { x: 51, y: 657, angle: -27 }, { x: 263, y: 982, angle: -53 }, { x: 462, y: 580, angle: 79 }, { x: 171, y: 775, angle: 44 }, { x: 294, y: 543, angle: 0 }, { x: 630, y: 455, angle: 139 }, { x: 472, y: 469, angle: -111 }, { x: 378, y: 963, angle: -140 }, { x: 242, y: 615, angle: -89 }, { x: 147, y: 364, angle: -21 }, { x: 546, y: 552, angle: 91 }, { x: 588, y: 902, angle: 25 }, { x: 110, y: 380, angle: -91 }, { x: 391, y: 828, angle: -36 }, { x: 746, y: 770, angle: -125 }, { x: 138, y: 292, angle: -9 }, { x: 31, y: 761, angle: 0 }, { x: 551, y: 286, angle: -46 }, { x: 548, y: 153, angle: 2 }, { x: 112, y: 750, angle: -127 }, { x: 681, y: 927, angle: 81 }, { x: 676, y: 413, angle: 13 }, { x: 625, y: 291, angle: 42 }, { x: 291, y: 257, angle: 20 }, { x: 633, y: 288, angle: -11 }, { x: 318, y: 155, angle: -30 }, { x: 351, y: 886, angle: -129 }, { x: 529, y: 845, angle: 35 }, { x: 367, y: 415, angle: 0 }, { x: 170, y: 216, angle: -47 }, { x: 559, y: 93, angle: -40 }, { x: 407, y: 307, angle: -62 }, { x: 529, y: 366, angle: -177 }, { x: 265, y: 798, angle: -167 }, { x: 398, y: 553, angle: -120 }, { x: 728, y: 594, angle: 9 }, { x: 329, y: 425, angle: 127 }, { x: 128, y: 880, angle: -9 }, { x: 697, y: 670, angle: 144 }, { x: 405, y: 929, angle: 63 }, { x: 482, y: 501, angle: -176 }, { x: 516, y: 391, angle: -94 }, { x: 538, y: 172, angle: -73 }, { x: 729, y: 491, angle: 133 }, { x: 636, y: 252, angle: 22 }, { x: 693, y: 369, angle: 57 }, { x: 111, y: 849, angle: -66 }, { x: 574, y: 349, angle: 106 }, { x: 607, y: 956, angle: 82 }, { x: 633, y: 227, angle: 0 }, { x: 164, y: 254, angle: 33 }, { x: 632, y: 338, angle: 72 }, { x: 646, y: 282, angle: 79 }, { x: 167, y: 190, angle: -7 }, { x: 121, y: 628, angle: 110 }, { x: 478, y: 690, angle: 118 }, { x: 95, y: 499, angle: -131 }, { x: 302, y: 1016, angle: 0 }, { x: 523, y: 655, angle: 45 }, { x: 216, y: 505, angle: -3 }, { x: 108, y: 905, angle: -63 }, { x: 89, y: 443, angle: -35 }, { x: 306, y: 533, angle: -135 }, { x: 726, y: 449, angle: 28 }, { x: 52, y: 383, angle: -50 }, { x: 215, y: 268, angle: -156 }, { x: 298, y: 105, angle: -45 }, { x: 737, y: 627, angle: 79 }, { x: 13, y: 751, angle: -105 }, { x: 410, y: 4, angle: 36 }, { x: 41, y: 252, angle: -43 }, { x: 547, y: 98, angle: 38 }, { x: 550, y: 992, angle: 106 }, { x: 197, y: 990, angle: -104 }, { x: 97, y: 671, angle: -75 }, { x: 337, y: 1028, angle: 143 }, { x: 494, y: 630, angle: -130 }, { x: 644, y: 633, angle: 24 }, { x: 691, y: 872, angle: 86 }, { x: 121, y: 908, angle: 100 }, { x: 509, y: 977, angle: -173 }, { x: 69, y: 423, angle: -79 }, { x: 213, y: 956, angle: -178 }, { x: 401, y: 274, angle: 0 }, { x: 745, y: 892, angle: 87 }, { x: 611, y: 910, angle: 97 }, { x: 310, y: 1008, angle: 180 },
            //500
            { x: 158, y: 810, angle: 0 }, { x: 613, y: 506, angle: -117 }, { x: 181, y: 445, angle: 3 }, { x: 612, y: 929, angle: -101 }, { x: 201, y: 261, angle: 47 }, { x: 167, y: 523, angle: -64 }, { x: 416, y: 780, angle: -18 }, { x: 227, y: 567, angle: 22 }, { x: 536, y: 361, angle: -36 }, { x: 423, y: 970, angle: -50 }, { x: 109, y: 340, angle: -23 }, { x: 358, y: 289, angle: -130 }, { x: 564, y: 327, angle: 57 }, { x: 346, y: 698, angle: 0 }, { x: 359, y: 217, angle: 31 }, { x: 303, y: 707, angle: 113 }, { x: 562, y: 275, angle: 32 }, { x: 272, y: 788, angle: -24 }, { x: 511, y: 948, angle: 30 }, { x: 50, y: 770, angle: -155 }, { x: 682, y: 656, angle: 105 }, { x: 433, y: 188, angle: -8 }, { x: 518, y: 997, angle: 154 }, { x: 291, y: 448, angle: 20 }, { x: 274, y: 775, angle: 149 }, { x: 512, y: 489, angle: 59 }, { x: 107, y: 451, angle: 37 }, { x: 745, y: 665, angle: 96 }, { x: 13, y: 615, angle: -3 }, { x: 317, y: 673, angle: -9 }, { x: 616, y: 770, angle: -89 }, { x: 199, y: 926, angle: 25 }, { x: 634, y: 383, angle: -98 }, { x: 272, y: 212, angle: -6 }, { x: 478, y: 97, angle: 102 }, { x: 687, y: 502, angle: 92 }, { x: 404, y: 702, angle: 8 }, { x: 441, y: 1027, angle: -99 }, { x: 700, y: 749, angle: -112 }, { x: 137, y: 759, angle: 0 }, { x: 284, y: 539, angle: 90 }, { x: 160, y: 402, angle: -33 }, { x: 325, y: 862, angle: 110 }, { x: 49, y: 648, angle: 99 }, { x: 218, y: 633, angle: 45 }, { x: 142, y: 796, angle: 115 }, { x: 103, y: 675, angle: -107 }, { x: 686, y: 734, angle: -32 }, { x: 610, y: 462, angle: 83 }, { x: 28, y: 605, angle: 49 }, { x: 152, y: 526, angle: 18 }, { x: 285, y: 757, angle: 14 }, { x: 758, y: 762, angle: -171 }, { x: 568, y: 559, angle: 122 }, { x: 308, y: 142, angle: 35 }, { x: 463, y: 481, angle: -37 }, { x: 335, y: 897, angle: 0 }, { x: 264, y: 391, angle: -42 }, { x: 476, y: 100, angle: -11 }, { x: 461, y: 855, angle: -141 }, { x: 682, y: 773, angle: 12 }, { x: 160, y: 585, angle: -27 }, { x: 46, y: 525, angle: 26 }, { x: 700, y: 364, angle: 21 }, { x: 504, y: 886, angle: -23 }, { x: 290, y: 1000, angle: 113 }, { x: 465, y: 518, angle: -137 }, { x: 248, y: 947, angle: 26 }, { x: 727, y: 587, angle: -43 }, { x: 449, y: 254, angle: 66 }, { x: 725, y: 329, angle: 69 }, { x: 450, y: 778, angle: 61 }, { x: 488, y: 640, angle: -67 }, { x: 232, y: 408, angle: 147 }, { x: 738, y: 327, angle: 8 }, { x: 672, y: 826, angle: -117 }, { x: 284, y: 755, angle: -151 }, { x: 511, y: 402, angle: 179 }, { x: 237, y: 938, angle: 163 }, { x: 581, y: 688, angle: 101 }, { x: 275, y: 206, angle: 52 }, { x: 266, y: 565, angle: 39 }, { x: 193, y: 261, angle: -106 }, { x: 450, y: 850, angle: -16 }, { x: 758, y: 665, angle: 65 }, { x: 306, y: 284, angle: -32 }, { x: 189, y: 992, angle: -55 }, { x: 691, y: 876, angle: -3 }, { x: 502, y: 698, angle: -78 }, { x: 412, y: 83, angle: 34 }, { x: 36, y: 247, angle: -115 }, { x: 536, y: 380, angle: 19 }, { x: 63, y: 771, angle: -55 }, { x: 494, y: 703, angle: 53 }, { x: 727, y: 439, angle: 74 }, { x: 443, y: 1022, angle: 124 }, { x: 164, y: 799, angle: -93 }, { x: 762, y: 828, angle: 86 }, { x: 42, y: 695, angle: -73 }, { x: 274, y: 1029, angle: -98 }
        ]
    };
});