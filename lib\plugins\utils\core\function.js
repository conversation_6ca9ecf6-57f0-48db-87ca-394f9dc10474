ig.module('plugins.utils.core.function')
.requires(
    'plugins.utils.core.types'
)
.defines(function () { 'use strict';

    var _g = ig.global;
    ig.utils = ig.utils || {};

    ig.utils.throttle = function (callback, delay, trailing) {
        var timeoutId; var timeLast = 0;
        delay = ig.utils.isNumber(delay) ? delay : ig.utils.DEFAULT_THROTTLE_DELAY;
        function execute(me, args) { timeLast = Date.now(); callback.apply(me, args); }
        return function throttled() {
            var me = this; var elapsed = Date.now() - timeLast; var args = arguments;
            if (elapsed > delay) { timeoutId && _g.clearTimeout(timeoutId); execute(me, args); }
            else if (trailing !== false) { timeoutId && _g.clearTimeout(timeoutId); timeoutId = _g.setTimeout(function(){ execute(me, args); }, delay - elapsed); }
        };
    };

    ig.utils.debounce = function (callback, delay) {
        var timeoutId; delay = ig.utils.isNumber(delay) ? delay : ig.utils.DEFAULT_THROTTLE_DELAY;
        return function debounced() {
            var me = this; var args = arguments; timeoutId && _g.clearTimeout(timeoutId);
            timeoutId = _g.setTimeout(function () { callback.apply(me, args); }, delay);
        };
    };
});

