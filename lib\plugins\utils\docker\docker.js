ig.module('plugins.utils.docker.docker')
.requires(
    'plugins.utils.docker.docker-mixin',
    'plugins.utils.docker.docker-manager'
)
.defines(function () {
    
    /**
     * Docker Plugin for ImpactJS
     * 
     * This plugin provides docking functionality that allows child entities to dock
     * to parent entities using percentage-based positioning and pixel offsets.
     * 
     * Features:
     * - Percentage-based positioning (0.0 to 1.0)
     * - Pixel offset support for fine-tuning
     * - Automatic position updates when parent moves
     * - Automatic cleanup when parent is destroyed
     * - Two usage methods: dockTo() method and dockerObject in settings
     * 
     * Usage Method 1 - Called from child entity:
     * this.dockTo(parentEntity, {
     *   percent: { x: 0.5, y: 0.5 },
     *   offset: { x: 10, y: 10 },
     *   isResponsive: false
     * });
     *
     * Usage Method 2 - During entity spawning:
     * var ent = ig.game.spawnEntity(EntityClass, x, y, {
     *   dockerObject: parentEntity,
     *   percent: { x: 0.5, y: 0.5 },
     *   offset: { x: 10, y: 10 },
     *   isResponsive: false
     * });
     * 
     * Position Calculation:
     * childX = parentX + (parentWidth * percent.x) + offset.x
     * childY = parentY + (parentHeight * percent.y) + offset.y
     */
    
    // Docker utility functions
    ig.Docker = {
        
        /**
         * Create a docked entity
         * @param {Function} entityClass - The entity class to spawn
         * @param {ig.Entity} parentEntity - The parent entity to dock to
         * @param {Object} dockerConfig - Docker configuration
         * @param {Object} spawnSettings - Additional spawn settings
         * @returns {ig.Entity} The spawned and docked entity
         */
        createDockedEntity: function(entityClass, parentEntity, dockerConfig, spawnSettings) {
            if (!entityClass || !parentEntity) {
                console.error('ig.Docker.createDockedEntity: Missing entityClass or parentEntity');
                return null;
            }
            
            // Merge docker config into spawn settings
            spawnSettings = spawnSettings || {};
            spawnSettings.dockerObject = parentEntity;
            spawnSettings.percent = dockerConfig.percent || { x: 0.5, y: 0.5 };
            spawnSettings.offset = dockerConfig.offset || { x: 0, y: 0 };
            spawnSettings.isResponsive = (typeof dockerConfig.isResponsive === 'boolean') ? dockerConfig.isResponsive : false;
            
            // Calculate initial spawn position (will be updated by docker system)
            var spawnX = parentEntity.pos.x + (parentEntity.size.x * spawnSettings.percent.x) + spawnSettings.offset.x;
            var spawnY = parentEntity.pos.y + (parentEntity.size.y * spawnSettings.percent.y) + spawnSettings.offset.y;
            
            // Spawn the entity
            return ig.game.spawnEntity(entityClass, spawnX, spawnY, spawnSettings);
        },
        
        /**
         * Dock an existing entity to a parent
         * @param {ig.Entity} childEntity - The entity to dock
         * @param {ig.Entity} parentEntity - The parent entity
         * @param {Object} dockerConfig - Docker configuration
         * @returns {boolean} True if docking was successful
         */
        dockEntity: function(childEntity, parentEntity, dockerConfig) {
            if (!childEntity || !parentEntity) {
                console.error('ig.Docker.dockEntity: Missing childEntity or parentEntity');
                return false;
            }
            
            if (!childEntity.dockTo || typeof childEntity.dockTo !== 'function') {
                console.error('ig.Docker.dockEntity: Child entity does not have docker functionality. Inject ig.DockerMixin first.');
                return false;
            }
            
            return childEntity.dockTo(parentEntity, dockerConfig);
        },
        
        /**
         * Undock an entity
         * @param {ig.Entity} entity - The entity to undock
         * @returns {boolean} True if undocking was successful
         */
        undockEntity: function(entity) {
            if (!entity) {
                console.error('ig.Docker.undockEntity: Missing entity');
                return false;
            }
            
            if (!entity.undock || typeof entity.undock !== 'function') {
                console.error('ig.Docker.undockEntity: Entity does not have docker functionality');
                return false;
            }
            
            entity.undock();
            return true;
        },
        
        /**
         * Get all entities docked to a parent
         * @param {ig.Entity} parentEntity - The parent entity
         * @returns {Array} Array of docked entities
         */
        getDockedEntities: function(parentEntity) {
            if (!ig.DockerManager) {
                console.error('ig.Docker.getDockedEntities: DockerManager not available');
                return [];
            }
            
            return ig.DockerManager.getEntitiesDockedTo(parentEntity);
        },
        
        /**
         * Undock all entities from a parent
         * @param {ig.Entity} parentEntity - The parent entity
         */
        undockAllFrom: function(parentEntity) {
            if (!ig.DockerManager) {
                console.error('ig.Docker.undockAllFrom: DockerManager not available');
                return;
            }
            
            ig.DockerManager.undockAllFrom(parentEntity);
        },
        
        /**
         * Get docker statistics
         * @returns {Object} Docker statistics
         */
        getStats: function() {
            if (!ig.DockerManager) {
                return { dockedEntityCount: 0, managerInitialized: false };
            }
            
            return {
                dockedEntityCount: ig.DockerManager.getDockedEntityCount(),
                managerInitialized: ig.DockerManager._initialized
            };
        }
    };
    
    // Convenience method to inject docker functionality into an entity class
    ig.Docker.injectInto = function(entityClass) {
        if (!entityClass || !ig.DockerMixin) {
            console.error('ig.Docker.injectInto: Invalid entityClass or DockerMixin not available');
            return false;
        }
        
        entityClass.inject(ig.DockerMixin);
        return true;
    };
    
    // Auto-inject docker functionality into EntityExtended if available
    if (ig.EntityExtended) {
        ig.EntityExtended.inject(ig.DockerMixin);
        console.log('Docker: Auto-injected into ig.EntityExtended');
    }
    
    // Auto-inject docker functionality into base Entity class
    ig.Entity.inject(ig.DockerMixin);
    console.log('Docker: Injected into ig.Entity');
    
    console.log('Docker Plugin: Loaded successfully');
});
