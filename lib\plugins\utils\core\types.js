ig.module('plugins.utils.core.types')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    // Basic type and array helpers
    ig.utils.type = function (o) {
        return o == null ? o + '' : Object.prototype.toString.call(o).slice(8, -1).toLowerCase();
    };

    ig.utils.isNumber = function (n) {
        return !isNaN(n) && isFinite(n) && typeof n !== 'boolean';
    };

    ig.utils.isArray = function (target) {
        return Object.prototype.toString.call(target) === '[object Array]';
    };

    ig.utils.toArray = function (target) {
        return target ? (ig.utils.isArray(target) !== true ? [target] : target) : [];
    };

    ig.utils.toNotArray = function (target, index) {
        return ig.utils.isArray(target) === true ? target[index || 0] : target;
    };

    // Shared constants
    ig.utils.DEFAULT_THROTTLE_DELAY = 500; // ms
});

