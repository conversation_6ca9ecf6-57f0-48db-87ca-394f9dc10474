ig.module('game.entities.objects.popup-settings')
.requires(
    'plugins.utils.objects.popup-base',
    'plugins.utils.buttons.button-factory',
    'plugins.utils.text.text',
    'game.entities.buttons.button-audio-sfx',
    'game.entities.buttons.button-audio-bgm',
    'game.entities.buttons.button-main-menu',
    'game.entities.buttons.button-restart'
)
.defines(function () {
    EntityPopupSettingsMenu = EntityPopupBase.extend({
        name: 'popup-settings-menu',
        elements: {},
        
        headerTextConfig: {
            text: _STRINGS['Game']['Settings'],
            fontSize: 48,
            fontFamily: 'montserrat',
            fontColor: '#000000',
            align: 'center',
            vAlign: 'middle'
            // width & height will be calculated based on popup size or can be set in settings
        },
        
        headerTextOffset: { x: 0, y: 0 },
        
        hasCloseButton: true,
        displayOverlay: false,

        init: function (x, y, settings) {
            if (settings.idleSheetInfo) this.idleSheetInfo = settings.idleSheetInfo;
            this.parent(x, y, settings); // Call parent's init

            this.elements.buttons = {};
            this.elements.buttons.sound = this.spawnEntity(
                EntityButtonAudioSFX,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.bgm = this.spawnEntity(
                EntityButtonAudioBGM,
                this.pos.x,
                this.pos.y
            );
        },

        enterCb: function () {}, // Callback after enter animation
        exitCb: function () { ig.game.currentController.tweenShow(); },  // Callback after exit animation
        onTweenUpdateEnter: function () {}, // Called during tween update
        onTweenUpdateExit: function () {}, // Called during tween update

        tweenEnter: function () {
            // this.isVisible = true;
            // this.isTweening = true;

            // var origPosY = this.pos.y;
            // this.pos.y += this.tweenPosY; // Start from below

            // var tweenObj = { popupAlpha: this.popupAlpha, posY: this.pos.y };

            // // Tween for alpha
            // new ig.TweenDef(tweenObj)
            // .to({ popupAlpha: 1 }, 300)
            // .onUpdate(function () {
            //     this.popupAlpha = tweenObj.popupAlpha;
            //     this.updateElementsAlpha(this.popupAlpha);
            // }.bind(this))
            // .onComplete(function () { // Ensure final alpha is set
            //     this.popupAlpha = 1;
            //     this.updateElementsAlpha(this.popupAlpha);
            // }.bind(this))
            // .start();

            // // Tween for position
            // new ig.TweenDef(tweenObj)
            // .easing(ig.Tween.Easing.Back.EaseOut)
            // .to({ posY: origPosY }, 300)
            // .onUpdate(function () {
            //     this.pos.y = tweenObj.posY;
            //     this.updateElementsPosition();
            // }.bind(this))
            // .onComplete(function () {
            //     this.pos.y = origPosY; // Ensure final position
            //     this.updateElementsPosition();
            //     this.isTweening = false;
            //     if (typeof this.enterCb === 'function') {
            //         this.enterCb();
            //     }
            // }.bind(this))
            // .start();
            this.parent();
        },

        tweenExit: function () {
            // this.isVisible = false;
            // this.isTweening = true;

            // var tweenObj = { popupAlpha: this.popupAlpha, posY: this.pos.y };
            // var targetPosY = this.pos.y + this.tweenPosY; // Target position when exiting

            // // Tween for alpha
            // new ig.TweenDef(tweenObj)
            // .to({ popupAlpha: 0 }, 200)
            // .onUpdate(function () {
            //     this.popupAlpha = tweenObj.popupAlpha;
            //     this.updateElementsAlpha(this.popupAlpha);
            // }.bind(this))
            // .onComplete(function () { // Ensure final alpha is set
            //     this.popupAlpha = 0;
            //     this.updateElementsAlpha(this.popupAlpha);
            // }.bind(this))
            // .start();

            // // Tween for position
            // new ig.TweenDef(tweenObj)
            // .easing(ig.Tween.Easing.Back.EaseIn)
            // .to({ posY: targetPosY }, 300)
            // .onUpdate(function () {
            //     this.pos.y = tweenObj.posY;
            //     this.updateElementsPosition();
            // }.bind(this))
            // .onComplete(function () {
            //     this.pos.y = targetPosY; // Ensure final position
            //     this.updateElementsPosition();
            //     this.isTweening = false;
            //     if (typeof this.exitCb === 'function') {
            //         this.exitCb();
            //     }
            //     this.delayedCall(0.05, this.kill.bind(this)); // Kill after animation
            // }.bind(this))
            // .start();
            this.parent();
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha);

            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent();

            if (this.elements.buttons) {
                var padding = 60;
                this.elements.buttons.sound.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.sound.size.x * 0.5);
                this.elements.buttons.sound.pos.y = this.pos.y + this.size.y * 0.5 - padding * 1.2;

                this.elements.buttons.bgm.pos.x = this.elements.buttons.sound.pos.x;
                this.elements.buttons.bgm.pos.y = this.elements.buttons.sound.pos.y + this.elements.buttons.sound.size.y + padding;

                this.elements.buttons.sound.textEntity._updateAnchorPosition();
                this.elements.buttons.bgm.textEntity._updateAnchorPosition();
            }

            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();
        },

        kill: function () {
            if (this.elements.buttons) {
                this.elements.buttons.sound.kill();
                this.elements.buttons.bgm.kill();                
            }
            this.parent();
        },

        draw: function () {
            this.parent();
        },
        
        update: function () {
            this.parent();
            // Any specific update logic for the popup itself
        },

        repos: function () {
            this.parent();
        }
    });

    EntityPopupSettingsGame = EntityPopupBase.extend({
        name: 'popup-settings-game',


        // Header text configuration, same as popup-settings
        headerTextConfig: {
            text: _STRINGS['Game']['Paused'],
            fontSize: 120,
            fontFamily: 'montserrat',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },
        
        // Offset for the header text relative to the popup's top
        headerTextOffset: { x: 0, y: 60 },

        // --- Popup Properties ---
        displayOverlay: false,
        hasCloseButton: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};

            this.elements.buttons.sound = this.spawnEntity(
                EntityButtonAudioSFX,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.bgm = this.spawnEntity(
                EntityButtonAudioBGM,
                this.pos.x,
                this.pos.y
            );

            this.elements.buttons.menu = this.spawnEntity(
                EntityButtonMainMenu,
                this.pos.x,
                this.pos.y
            );
            this.elements.buttons.restart = this.spawnEntity(
                EntityButtonRestart,
                this.pos.x,
                this.pos.y
            );

            ig.game.sortEntitiesDeferred();
        },

        updateElementsAlpha: function (alpha) {
            this.parent(alpha); // Update base elements (header, close button, overlay)

            // Update all buttons
            if (this.elements.buttons) {
                for (var key in this.elements.buttons) {
                    if (this.elements.buttons[key] && typeof this.elements.buttons[key].updateAlpha === 'function') {
                        this.elements.buttons[key].updateAlpha(alpha);
                    }
                }
            }
        },

        updateElementsPosition: function () {
            this.parent(); // Update base elements (header, close button)

            if (this.elements.buttons) {
                // Position the audio buttons
                var audioPadding = 60;
                this.elements.buttons.sound.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.sound.size.x * 0.5);
                this.elements.buttons.sound.pos.y = this.pos.y + this.size.y * 0.5 - audioPadding * 1.8;

                this.elements.buttons.bgm.pos.x = this.elements.buttons.sound.pos.x;
                this.elements.buttons.bgm.pos.y = this.elements.buttons.sound.pos.y + this.elements.buttons.sound.size.y + audioPadding;

                // Position the navigation buttons
                var navPadding = 20;
                this.elements.buttons.menu.pos.x = this.pos.x + (this.size.x * 0.5) - (this.elements.buttons.menu.size.x) - navPadding;
                this.elements.buttons.menu.pos.y = this.pos.y + this.size.y * 0.8;

                this.elements.buttons.restart.pos.x = this.pos.x + (this.size.x * 0.5) + navPadding;
                this.elements.buttons.restart.pos.y = this.elements.buttons.menu.pos.y;
                
                // Update anchor positions for all button text entities
                // this.elements.buttons.sound.textEntity._updateAnchorPosition();
                // this.elements.buttons.bgm.textEntity._updateAnchorPosition();
                // this.elements.buttons.menu.textEntity._updateAnchorPosition();
                // this.elements.buttons.restart.textEntity._updateAnchorPosition();
            }

            if (this.elements.headerText) {
                this.elements.headerText._updateAnchorPosition();
            }
        },

        exitCb: function () {
            // ig.currentCtrl.resumeGame();
        },

        kill: function () {
            // Kill all buttons
            if (this.elements.buttons) {
                this.elements.buttons.sound.kill();
                this.elements.buttons.bgm.kill();
                this.elements.buttons.menu.kill();
                this.elements.buttons.restart.kill();                
            }
            this.parent(); // Kill base elements
        },

        update: function () {
            this.parent();
        }
    });
});