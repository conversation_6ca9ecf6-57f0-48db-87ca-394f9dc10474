ig.module('plugins.utils.time.format')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    /**
     * Formats seconds into MM:SS format.
     * @param {Number} seconds Total seconds
     * @returns {String} Formatted time string
     */
    ig.utils.toMMSS = function (seconds) {
        // Round down to nearest whole number to avoid decimals
        var wholeSeconds = Math.floor(seconds);

        var minutes = Math.floor(wholeSeconds / 60);
        var remainingSeconds = wholeSeconds % 60;

        // Custom function to pad the number with leading zeros
        function pad (number) {
            return number < 10 ? '0' + number : number;
        }

        return pad(minutes) + ':' + pad(remainingSeconds);
    };
});
