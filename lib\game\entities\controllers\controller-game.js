ig.module('game.entities.controllers.controller-game')
.requires(
    'impact.entity',
    'plugins.utils.entity-extended',
    'game.entities.buttons.button-pause',
    'plugins.utils.transition',
    'game.entities.objects.tree',
    'game.game-data'
)
.defines(function () {
    "use strict";

    ig.ControllerGame = ig.global.ControllerGame = ig.EntityExtended.extend({
        zIndex: 1,
        name: 'controller-game',
        buttons: {},

        titleImage: new ig.Image('media/graphics/sprites/ui/etc/title.png'),

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            ig.game.currentController = this;
            this.ctx = ig.system.context;

            this.transition = ig.game.spawnEntity(EntityTransition, 0, 0, { color: 'black' });

            this.postInit();
            this.transition.fadeOut(200);

            ig.sizeHandler.reorient();
        },

        postInit: function () {
            this.setup();
            this.isReady = true;
        },

        setup: function () {
            this.buttons = {};

            // === Setup Custom Functions
            this.buttons.updateAlpha = function (alpha) {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].updateAlpha === 'function') {
                        this.buttons[key].updateAlpha(alpha);
                    }
                }
            }.bind(this);

            this.buttons.disable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].disable === 'function') {
                        this.buttons[key].disable();
                    }
                }
            }.bind(this);

            this.buttons.enable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].enable === 'function') {
                        this.buttons[key].enable();
                    }
                }
            }.bind(this);
            // === End Setup Custom Functions
            
            var padding = 40;
            this.buttons.pause = this.spawnEntity(EntityButtonPause, -999, -999);

            this.tree = this.spawnEntity(EntityTree, ig.system.width * 0.5, ig.system.height * 0.5);
            
            this.repos();
            ig.game.sortEntitiesDeferred();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();

            this.ctx.save();
            
            var grad = this.ctx.createLinearGradient(0, 0, 0, ig.system.height);
            grad.addColorStop(0, "#44B7E7"); 
            grad.addColorStop(1, "#E0F8FF"); 
            this.ctx.fillStyle = grad;
            this.ctx.fillRect(0, 0, ig.system.width, ig.system.height);

            if (this.tree) {
                
            }

            this.ctx.restore();
        },

        repos: function () {
            if (Object.keys(this.buttons).length > 0) {
                this.buttons.pause.pos.x = ig.system.width - (this.buttons.pause.size.x + 20) + ig.game.screen.x;
                this.buttons.pause.pos.y = 20 + ig.game.screen.y;
            }
        }
    });
});
