ig.module('plugins.utils.docker.docker-manager')
.requires(
    'impact.game'
)
.defines(function () {
    
    /**
     * Docker Manager - Manages all docking relationships and updates
     * 
     * This manager handles:
     * - Registration and unregistration of docked entities
     * - Automatic position updates for docked entities
     * - Cleanup of orphaned docking relationships
     */
    ig.DockerManager = {
        
        // Registry of all docked entities
        _dockedEntities: {},
        
        // Flag to track if manager is initialized
        _initialized: false,
        
        /**
         * Initialize the docker manager
         * This is called automatically when the first entity is docked
         */
        init: function() {
            if (this._initialized) return;
            
            this._dockedEntities = {};
            this._initialized = true;
            
            console.log('DockerManager: Initialized');
        },
        
        /**
         * Register a docked entity with the manager
         * @param {ig.Entity} entity - The docked entity
         */
        registerDockedEntity: function(entity) {
            if (!this._initialized) {
                this.init();
            }
            
            if (!entity || !entity._dockerId) {
                console.error('DockerManager.registerDockedEntity: Invalid entity or missing docker ID');
                return;
            }
            
            this._dockedEntities[entity._dockerId] = entity;
            console.log('DockerManager: Registered docked entity with ID:', entity._dockerId);
        },
        
        /**
         * Unregister a docked entity from the manager
         * @param {string} dockerId - The docker ID of the entity to unregister
         */
        unregisterDockedEntity: function(dockerId) {
            if (!dockerId || !this._dockedEntities[dockerId]) {
                return;
            }
            
            delete this._dockedEntities[dockerId];
            console.log('DockerManager: Unregistered docked entity with ID:', dockerId);
        },
        
        /**
         * Update all docked entities
         * This should be called every frame to keep docked entities in sync
         */
        updateDockedEntities: function() {
            if (!this._initialized) return;
            
            var entitiesToRemove = [];
            
            for (var dockerId in this._dockedEntities) {
                var entity = this._dockedEntities[dockerId];
                
                // Check if entity is still alive and docked
                if (!entity || entity._killed || !entity._isDocked) {
                    entitiesToRemove.push(dockerId);
                    continue;
                }
                
                // Check if parent is still alive
                if (!entity._dockerParent || entity._dockerParent._killed) {
                    entity.undock();
                    entitiesToRemove.push(dockerId);
                    continue;
                }
                
                // Update the entity's position
                entity._updateDockedPosition();
            }
            
            // Clean up dead entities
            for (var i = 0; i < entitiesToRemove.length; i++) {
                this.unregisterDockedEntity(entitiesToRemove[i]);
            }
        },
        
        /**
         * Get the number of currently docked entities
         * @returns {number} Number of docked entities
         */
        getDockedEntityCount: function() {
            return Object.keys(this._dockedEntities).length;
        },
        
        /**
         * Get all docked entities
         * @returns {Array} Array of docked entities
         */
        getAllDockedEntities: function() {
            var entities = [];
            for (var dockerId in this._dockedEntities) {
                entities.push(this._dockedEntities[dockerId]);
            }
            return entities;
        },
        
        /**
         * Find all entities docked to a specific parent
         * @param {ig.Entity} parentEntity - The parent entity
         * @returns {Array} Array of entities docked to the parent
         */
        getEntitiesDockedTo: function(parentEntity) {
            if (!parentEntity) return [];
            
            var dockedToParent = [];
            for (var dockerId in this._dockedEntities) {
                var entity = this._dockedEntities[dockerId];
                if (entity._dockerParent === parentEntity) {
                    dockedToParent.push(entity);
                }
            }
            return dockedToParent;
        },
        
        /**
         * Undock all entities from a specific parent
         * @param {ig.Entity} parentEntity - The parent entity
         */
        undockAllFrom: function(parentEntity) {
            if (!parentEntity) return;
            
            var entitiesToUndock = this.getEntitiesDockedTo(parentEntity);
            for (var i = 0; i < entitiesToUndock.length; i++) {
                entitiesToUndock[i].undock();
            }
        },
        
        /**
         * Clear all docking relationships
         */
        clear: function() {
            for (var dockerId in this._dockedEntities) {
                var entity = this._dockedEntities[dockerId];
                if (entity && entity.undock) {
                    entity.undock();
                }
            }
            this._dockedEntities = {};
        },
        
        /**
         * Reset the manager
         */
        reset: function() {
            this.clear();
            this._initialized = false;
        }
    };
    
    // Extend ig.Game to automatically update docked entities
    ig.Game.inject({
        update: function() {
            this.parent();
            
            // Update all docked entities after the main game update
            if (ig.DockerManager) {
                ig.DockerManager.updateDockedEntities();
            }
        }
    });
    
    // Extend ig.Entity to automatically initialize docker functionality
    ig.Entity.inject({
        init: function(x, y, settings) {
            this.parent(x, y, settings);
            
            // Initialize docker functionality if the mixin is present
            if (this.initDocker && typeof this.initDocker === 'function') {
                this.initDocker(settings);
            }
        }
    });
});
