ig.module('game.${_toDotCase(relativeFileDirname.split("\\").pop())}.${fileBasenameNoExtension}.${input.fileName}')
.requires(
    'plugins.utils.entity-extended'
)
.defines(function () {
    "use strict";

    ig.Entity${_toPascalCase(input.fileName)} = ig.global.Entity${_toPascalCase(input.fileName)} = ig.EntityExtended.extend({
        name: '${input.fileName}',
		// idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/buttons/btn-blank.png'), frameCountX: 1, frameCountY: 1 },
		isClickable: false,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            ig.game.sortEntitiesDeferred();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
