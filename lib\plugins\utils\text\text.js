ig.module(
    'plugins.utils.text.text'
)
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.text.text-to-canvas'
)
.defines(function () {
    // Consolidated Text service and mixin
    // Provides a single place to access text rendering helpers and defaults
    ig.Text = ig.Text || {};

    // Canonical defaults for text rendering
    ig.Text.defaults = {
        fontSize: 14,
        fontFamily: 'Arial',
        fontColor: '#000',
        text: '',
        width: 100,
        height: 50,
        align: 'center',
        vAlign: 'middle',
        strokeColor: '#000',
        strokeWidth: 0,
        justify: false,
        inferWhitespace: true,
        overflow: true,
        debug: false
    };

	    /**
	     * Quick usage examples:
	     *
	     * // Static helpers on ig.Text operating on an entity or config
	     * ig.Text.setText(myEntity, 'Hello');
	     * ig.Text.updateFontSize(myEntity, 24);
	     * ig.Text.centerInEntity(myEntity);
	     * ig.Text.fadeIn(myEntity, 300);
	     *
	     * // Chainable instance helpers via ig.TextLabelMixin
	     * myEntity
	     *   .setTextContent('Score: 0')
	     *   .setFontColor('#ff0')
	     *   .setStroke('#000', 2)
	     *   .centerTextInEntity()
	     *   .fadeInText(300);
	     */


    // Thin wrapper over the low-level engine so consumers only need this module
    ig.Text.draw = function (ctx, text, config) {
        if (!ig.textToCanvas || !ctx) { return; }
        ig.textToCanvas.drawText(ctx, text, config || {});
    };

    ig.Text.getTextHeight = function (ctx, text, style) {
        if (!ig.textToCanvas || !ctx) { return 0; }
        try {
            return ig.textToCanvas.getTextHeight({ ctx: ctx, text: text, style: style });
        } catch (e) {
            return 0;
        }
    };

	    // ------------------------------
	    // Convenience methods (ig.Text)
	    // ------------------------------
	    /**
	     * Get text from an entity or a plain config.
	     * @param {Object} target - Entity with textConfig or a plain config object.
	     * @returns {string} text
	     */
	    ig.Text.getText = function (target) {
	        if (!target) return '';
	        if (target.textConfig && typeof target.textConfig.text !== 'undefined') return target.textConfig.text;
	        if (typeof target.text !== 'undefined') return target.text;
	        return '';
	    };

	    /**
	     * Set text on an entity or config.
	     * @param {Object} target - Entity with textConfig or a plain config object.
	     * @param {string} text - New text.
	     * @returns {Object} target
	     */
	    ig.Text.setText = function (target, text) {
	        if (!target) return target;
	        if (target.textConfig) { target.textConfig.text = text; }
	        else { target.text = text; }
	        return target;
	    };

	    /**
	     * Append text to an entity or config.
	     * @param {Object} target
	     * @param {string} extra
	     * @returns {Object} target
	     */
	    ig.Text.appendText = function (target, extra) {
	        var cur = ig.Text.getText(target) || '';
	        return ig.Text.setText(target, cur + (extra || ''));
	    };

	    /**
	     * Clear text content.
	     * @param {Object} target
	     * @returns {Object} target
	     */
	    ig.Text.clearText = function (target) { return ig.Text.setText(target, ''); };

	    /**
	     * Update multiple visual properties on target.
	     * TODO: REVIEW - potentially redundant with updateTextConfig
	     * @param {Object} target - Entity or plain config
	     * @param {Object} style - Partial style to merge
	     */
	    ig.Text.setStyle = function (target, style) {
	        if (!target || !style) return;
	        var cfg = (target.textConfig) ? target.textConfig : target;
	        ig.merge(cfg, style);
	    };

	    /**
	     * Update font size.
	     * @param {Object} target
	     * @param {number} fontSize
	     */
	    ig.Text.updateFontSize = function (target, fontSize) { ig.Text.setStyle(target, { fontSize: fontSize }); };
	    /** Update font color. @param {Object} target @param {string} fontColor */
	    ig.Text.updateFontColor = function (target, fontColor) { ig.Text.setStyle(target, { fontColor: fontColor }); };
	    /** Update font family. @param {Object} target @param {string} fontFamily */
	    ig.Text.updateFontFamily = function (target, fontFamily) { ig.Text.setStyle(target, { fontFamily: fontFamily }); };
	    /** Update align. @param {Object} target @param {('left'|'center'|'right')} align */
	    ig.Text.updateAlign = function (target, align) { ig.Text.setStyle(target, { align: align }); };
	    /** Update vAlign. @param {Object} target @param {('top'|'middle'|'bottom')} vAlign */
	    ig.Text.updateVAlign = function (target, vAlign) { ig.Text.setStyle(target, { vAlign: vAlign }); };
	    /** Set stroke. @param {Object} target @param {string} strokeColor @param {number} strokeWidth */

            /** Set stroke. @param {Object} target @param {string} strokeColor @param {number} strokeWidth */
            ig.Text.setStroke = function (target, strokeColor, strokeWidth) { ig.Text.setStyle(target, { strokeColor: strokeColor, strokeWidth: strokeWidth }); };
            /** Set shadow.
             * @param {Object} target
             * @param {{enabled?:boolean, offsetX?:number, offsetY?:number, blur?:number, color?:string}} shadow
             */
            ig.Text.setShadow = function (target, shadow) {
                if (!target || !shadow) return;
                var cfg = (target.textConfig) ? target.textConfig : target;
                cfg.shadowEnabled = (typeof shadow.enabled === 'boolean') ? shadow.enabled : true;
                if (typeof shadow.offsetX === 'number') cfg.shadowOffsetX = shadow.offsetX;
                if (typeof shadow.offsetY === 'number') cfg.shadowOffsetY = shadow.offsetY;
                if (typeof shadow.blur === 'number') cfg.shadowBlur = shadow.blur;
                if (typeof shadow.color === 'string') cfg.shadowColor = shadow.color;
            };

	    /** Move text box by dx, dy (uses textOffset if available).
	     * @param {Object} entity - Entity using TextLabelMixin or EntityText
	     * @param {number} dx
	     * @param {number} dy
	     */
	    ig.Text.moveBy = function (entity, dx, dy) {
	        if (!entity) return;
	        entity.textOffset = entity.textOffset || { x: 0, y: 0 };
	        entity.textOffset.x += dx || 0;
	        entity.textOffset.y += dy || 0;
	    };

	    /** Center the text box inside entity size using textConfig.width/height. */
	    ig.Text.centerInEntity = function (entity) {
	        if (!entity || !entity.size || !entity.textConfig) return;
	        entity.textOffset = entity.textOffset || { x: 0, y: 0 };
	        entity.textOffset.x = Math.round((entity.size.x - (entity.textConfig.width || 0)) / 2);
	        entity.textOffset.y = Math.round((entity.size.y - (entity.textConfig.height || 0)) / 2);
	    };

	    /** Get configured box dimensions (not content).
	     * @param {Object} target
	     * @returns {{width:number,height:number}}
	     */
	    ig.Text.getDimensions = function (target) {
	        var cfg = (target && target.textConfig) ? target.textConfig : target;
	        return { width: (cfg && cfg.width) || 0, height: (cfg && cfg.height) || 0 };
	    };

	    // Internal: measure by rendering to an offscreen context
	    ig.Text._measure = function (text, config) {
	        if (!ig.textToCanvas) return { width: (config && config.width) || 0, height: 0 };
	        var w = Math.max(1, config.width || 1), h = Math.max(1, config.height || 1);
	        var canvas;
	        if (typeof OffscreenCanvas !== 'undefined') canvas = new OffscreenCanvas(w, h);
	        else if (typeof document !== 'undefined') canvas = document.createElement('canvas');
	        else return { width: w, height: 0 };
	        canvas.width = w; canvas.height = h;
	        var ctx = canvas.getContext('2d');
	        var result = ig.textToCanvas.drawText(ctx, text || '', config || {});
	        return { width: w, height: (result && result.height) || 0 };
	    };

	    /**
	     * Measure text bounds for given text and config without drawing to screen.
	     * @param {string} text
	     * @param {Object} config - layout and style (width/height, font, etc.)
	     * @returns {{width:number,height:number}}
	     */
	    ig.Text.measureBounds = function (text, config) { return ig.Text._measure(text, config); };

	    /**
	     * Check if text fits within config.width x config.height.
	     * @param {string} text
	     * @param {Object} config
	     * @returns {boolean}
	     */
	    ig.Text.fitsInBounds = function (text, config) {
	        var b = ig.Text._measure(text, config);
	        return b.height <= ((config && config.height) || 0);
	    };

	    /**
	     * Auto-size font to fit within width/height by searching fontSize.
	     * @param {Object} target - entity or config
	     * @param {{min?:number,max?:number,apply?:boolean,precision?:number}} opts
	     * @returns {number} The chosen fontSize
	     */
	    ig.Text.autoSizeText = function (target, opts) {
	        opts = opts || {}; var apply = !!opts.apply;
	        var cfg = (target && target.textConfig) ? target.textConfig : (target || {});
	        if (!cfg || !cfg.width || !cfg.height) return (cfg && cfg.fontSize) || 0;
	        var text = cfg.text || '';
	        var min = Math.max(1, opts.min || 8), max = Math.max(min, opts.max || (cfg.fontSize || 32));
	        var best = min, lo = min, hi = max, precision = Math.max(1, opts.precision || 1);
	        // Binary search largest font size that fits
	        while (lo <= hi) {
	            var mid = Math.floor((lo + hi) / 2);
	            var testCfg = ig.copy ? ig.copy(cfg) : JSON.parse(JSON.stringify(cfg));
	            testCfg.fontSize = mid;
	            var fits = ig.Text.fitsInBounds(text, testCfg);
	            if (fits) { best = mid; lo = mid + 1; } else { hi = mid - 1; }
	        }
	        // Snap to precision
	        best = Math.floor(best / precision) * precision;
	        if (apply) { cfg.fontSize = best; }
	        return best;
	    };

	    /** Fade in an entity by tweening its alpha. */
	    ig.Text.fadeIn = function (entity, duration) {
	        if (!entity) return null; duration = duration || 200;
	        var orig = { a: entity.alpha || 0 };
	        entity.alpha = orig.a;
	        return new ig.TweenDef(orig).to({ a: 1 }, duration).onUpdate(function () { entity.alpha = orig.a; }).start();
	    };
	    /** Fade out an entity by tweening its alpha. */
	    ig.Text.fadeOut = function (entity, duration) {
	        if (!entity) return null; duration = duration || 200;
	        var orig = { a: entity.alpha || 1 };
	        entity.alpha = orig.a;
	        return new ig.TweenDef(orig).to({ a: 0 }, duration).onUpdate(function () { entity.alpha = orig.a; }).start();
	    };
	    /**
	     * Tween text-related properties such as textOffset or textConfig fields.
	     * @param {Object} entity
	     * @param {Object} props - Nested: { textOffset?:{x,y}, textConfig?:{...}, alpha?, visualScale? }
	     * @param {number} duration
	     * @returns {ig.TweenDef}
	     */
	    ig.Text.tweenText = function (entity, props, duration) {
	        if (!entity || !props) return null; duration = duration || 200;
	        var state = {
	            ox: (entity.textOffset && entity.textOffset.x) || 0,
	            oy: (entity.textOffset && entity.textOffset.y) || 0,
	            a: (typeof entity.alpha === 'number') ? entity.alpha : 1,
	            s: (typeof entity.visualScale === 'number') ? entity.visualScale : 1
	        };
	        var target = {
	            ox: (props.textOffset && typeof props.textOffset.x === 'number') ? props.textOffset.x : state.ox,
	            oy: (props.textOffset && typeof props.textOffset.y === 'number') ? props.textOffset.y : state.oy,
	            a: (typeof props.alpha === 'number') ? props.alpha : state.a,
	            s: (typeof props.visualScale === 'number') ? props.visualScale : state.s
	        };
	        var tween = new ig.TweenDef(state).to(target, duration).onUpdate(function () {
	            entity.textOffset = entity.textOffset || { x: 0, y: 0 };
	            entity.textOffset.x = state.ox; entity.textOffset.y = state.oy;
	            if (typeof entity.alpha === 'number') entity.alpha = state.a;
	            if (typeof entity.setTextScale === 'function') entity.setTextScale(state.s);
	            else if (typeof entity.visualScale === 'number') entity.visualScale = state.s; // may not be used by mixin-only
	        });
	        // Text config fields (tween numerics only)
	        if (props.textConfig && entity.textConfig) {
	            var numericKeys = ['fontSize', 'strokeWidth', 'width', 'height'];
	            numericKeys.forEach(function (k) {
	                if (typeof props.textConfig[k] === 'number') {
	                    state[k] = entity.textConfig[k];
	                    target[k] = props.textConfig[k];
	                }
	            });
	            tween.onUpdate(function () {
	                numericKeys.forEach(function (k) { if (typeof state[k] === 'number') entity.textConfig[k] = state[k]; });
	            });
	        }
	        return tween.start();
	    };


    // New mixin co-located with Text helpers. Kept method names for compatibility.
    ig.TextLabelMixin = {
        // Start with shared defaults
        defaultTextConfig: (ig.copy ? ig.copy(ig.Text.defaults) : JSON.parse(JSON.stringify(ig.Text.defaults))),

        // Text position offset relative to entity position
        textOffset: { x: 0, y: 0 },

        // Initialize text configuration
        initText: function (config) {
            if (!ig.textToCanvas) {
                console.error('TextToCanvas plugin is required for TextLabelMixin');
                return;
            }

            // Create a new textConfig starting with mixin defaults
            this.textConfig = ig.copy ? ig.copy(this.defaultTextConfig) : JSON.parse(JSON.stringify(this.defaultTextConfig));

            // Apply entity-specific defaults if present
            if (this.entityTextConfig) { ig.merge(this.textConfig, this.entityTextConfig); }

            // Apply instance-specific config from parameters (highest priority)
            if (config) { ig.merge(this.textConfig, config); }

            // Handle text offsets similarly
            if (this.entityTextOffset) { ig.merge(this.textOffset, this.entityTextOffset); }

            // Set default size if not specified based on entity dimensions
            if (!this.textConfig.width && this.size) { this.textConfig.width = this.size.x * 0.9; }
            if (!this.textConfig.height && this.size) { this.textConfig.height = this.size.y * 0.9; }

            // Store original font size for scaling
            this._originalFontSize = this.textConfig.fontSize;
        },

        // Update the text content
        setText: function (text) {
            if (text !== undefined) {
                this.textConfig.text = text;
                // Trigger redraw for entities that support it
                if (typeof this.needsRedraw !== 'undefined') {
                    this.needsRedraw = true;
                }
            }
        },

        // Update text configuration properties
        updateTextConfig: function (config) {
            if (!config) return;
            var changed = false;
            var oldFontSize = this.textConfig.fontSize;
            
            // Check if any properties actually changed
            for (var key in config) {
                if (config.hasOwnProperty(key) && this.textConfig[key] !== config[key]) {
                    changed = true;
                    break;
                }
            }
            if (!changed && config.width && this.textConfig.width !== config.width) changed = true;
            if (!changed && config.height && this.textConfig.height !== config.height) changed = true;
            if (!changed) return;
            
            ig.merge(this.textConfig, config);
            if (config.fontSize && config.fontSize !== oldFontSize) {
                this._originalFontSize = this.textConfig.fontSize;
            }
            
            // Trigger redraw for entities that support it
            if (typeof this.needsRedraw !== 'undefined') {
                this.needsRedraw = true;
            }
        },

        // Draw text on the entity (for direct-use entities)
        drawText: function () {
            if (!this.textConfig || !this.textConfig.text || !ig.textToCanvas) { return; }

            // For entities with anchored text behavior, ensure position sync
            if (this._isAnchored && typeof this._updateAnchorPosition === 'function') {
                this._updateAnchorPosition();
            }

            var ctx = ig.system.context;
            var posX = this.pos.x - ig.game.screen.x;
            var posY = this.pos.y - ig.game.screen.y;

            var renderConfig = {
                x: ig.system.getDrawPos(posX + this.textOffset.x),
                y: ig.system.getDrawPos(posY + this.textOffset.y),
                width: this.textConfig.width,
                height: this.textConfig.height
            };

            // Copy remaining properties
            for (var key in this.textConfig) {
                if (key !== 'width' && key !== 'height') {
                    renderConfig[key] = this.textConfig[key];
                }
            }

            if (this.textConfig.fontFamily) {
                renderConfig.fontFamily = this.textConfig.fontFamily;
            }

            ctx.save();
            ig.Text.draw(ctx, this.textConfig.text, renderConfig);
            ctx.restore();
        },

        // ------------------------------
        // Convenience methods (TextLabelMixin instance)
        // ------------------------------
        /** Get current text. */
        getTextContent: function () { return (this.textConfig && this.textConfig.text) || ''; },
        /** Set text content (alias of setText). */
        setTextContent: function (text) {
            // Handle type conversion and validation like EntityText does
            if (typeof text === 'number') text = text.toString();
            if (typeof text !== 'string') return this;
            if (this.textConfig && this.textConfig.text === text) return this; // No change needed
            
            this.setText(text);
            return this;
        },
        /** Append text to current content. */
        appendText: function (extra) { this.setText(((this.textConfig && this.textConfig.text) || '') + (extra || '')); return this; },
        /** Clear text content. */
        clearText: function () { this.setText(''); return this; },

        /**
         * TODO: REVIEW - potentially redundant with updateTextConfig
         * Update multiple style properties.
         * @param {Object} style
         */
        setStyle: function (style) { if (style) this.updateTextConfig(style); return this; },
        /** Set font size. @param {number} fontSize */
        setFontSize: function (fontSize) { this.updateTextConfig({ fontSize: fontSize }); return this; },
        /** Set font color. @param {string} fontColor */
        setFontColor: function (fontColor) { this.updateTextConfig({ fontColor: fontColor }); return this; },
        /** Set font family. @param {string} fontFamily */
        setFontFamily: function (fontFamily) { this.updateTextConfig({ fontFamily: fontFamily }); return this; },
        /** Set align. @param {('left'|'center'|'right')} align */
        setAlign: function (align) { this.updateTextConfig({ align: align }); return this; },
        /** Set vertical align. @param {('top'|'middle'|'bottom')} vAlign */
        setVAlign: function (vAlign) { this.updateTextConfig({ vAlign: vAlign }); return this; },
        /** Set stroke style. */
        setStroke: function (strokeColor, strokeWidth) { this.updateTextConfig({ strokeColor: strokeColor, strokeWidth: strokeWidth }); return this; },
        /** Set shadow style. */
        setShadow: function (opts) {
            opts = opts || {}; var upd = { shadowEnabled: true };
            if (typeof opts.enabled === 'boolean') upd.shadowEnabled = opts.enabled;
            if (typeof opts.offsetX === 'number') upd.shadowOffsetX = opts.offsetX;
            if (typeof opts.offsetY === 'number') upd.shadowOffsetY = opts.offsetY;
            if (typeof opts.blur === 'number') upd.shadowBlur = opts.blur;
            if (typeof opts.color === 'string') upd.shadowColor = opts.color;
            this.updateTextConfig(upd); return this;
        },

        /** Move the text box by dx,dy using textOffset. */
        moveTextBy: function (dx, dy) { this.textOffset = this.textOffset || { x: 0, y: 0 }; this.textOffset.x += dx || 0; this.textOffset.y += dy || 0; return this; },
        /** Center the text box inside entity size using width/height. */
        centerTextInEntity: function () {
            if (!this.size || !this.textConfig) return this; this.textOffset = this.textOffset || { x: 0, y: 0 };
            this.textOffset.x = Math.round((this.size.x - (this.textConfig.width || 0)) / 2);
            this.textOffset.y = Math.round((this.size.y - (this.textConfig.height || 0)) / 2);
            return this;
        },
        /** Get configured text box dimensions. */
        getTextDimensions: function () { return { width: (this.textConfig && this.textConfig.width) || 0, height: (this.textConfig && this.textConfig.height) || 0 }; },

        /** Fade in by tweening alpha. */
        fadeInText: function (duration) { return ig.Text.fadeIn(this, duration); },
        /** Fade out by tweening alpha. */
        fadeOutText: function (duration) { return ig.Text.fadeOut(this, duration); },
        /** Scale text visually (uses setTextScale if available). */
        scaleTextTo: function (scale) { if (typeof this.setTextScale === 'function') this.setTextScale(scale); else this.visualScale = scale; return this; },
        
        /** Set text scale (EntityText functionality). */
        setTextScale: function (scale) {
            if (typeof this.visualScale !== 'undefined' && this.visualScale === scale) return this;
            if (typeof this.visualScale !== 'undefined') this.visualScale = scale;
            return this;
        },
        /** Tween text properties (delegates to ig.Text.tweenText). */
        tweenTextProps: function (props, duration) { return ig.Text.tweenText(this, props, duration); },

        /** Measure content bounds using the engine. */
        measureTextBounds: function () { return ig.Text.measureBounds((this.textConfig && this.textConfig.text) || '', this.textConfig || {}); },
        /** Check if current text fits within configured width/height. */
        textFitsInBounds: function () { return ig.Text.fitsInBounds((this.textConfig && this.textConfig.text) || '', this.textConfig || {}); },
        /** Auto-size current font to fit bounds; apply result by default. */
        autoSizeCurrentText: function (opts) { return ig.Text.autoSizeText(this, ig.merge({ apply: true }, opts || {})); }
    };


    EntityText = ig.EntityExtended.extend({
        size: { x: 1, y: 1 },
        alpha: 1,
        visualScale: 1,
        gravityFactor: 0,
        collides: ig.Entity.COLLIDES.NEVER,

        useOffscreenCanvas: true,
        offCanvas: null,
        offCanvasCTX: null,
        imageBitmap: null,
        needsRedraw: true,

        _anchorTargetEntity: null,
        _targetAnchorFactor: { x: 0, y: 0 },
        _selfAnchorFactor: { x: 0, y: 0 },
        _anchorPixelOffset: { x: 0, y: 0 },
        _isAnchored: false,

        entityTextConfig: {
            text: 'Text Entity',
            fontSize: 16,
            fontFamily: 'Arial',
            fontColor: '#FFFFFF',
            width: 100,
            height: 30,
            align: 'left',
            vAlign: 'top',
            strokeColor: '#000000',
            strokeWidth: 0,
            justify: false,
            inferWhitespace: true,
            overflow: true,      // Default to true to allow text to use allowance
            debug: false,
            // --- Shadow Properties ---
            shadowEnabled: false,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowBlur: 3,
            shadowColor: 'rgba(0,0,0,0.5)',
            // --- Overflow Allowance ---
            overflowAllowance: 5 // Pixels of padding on each side within the off-screen canvas
        },

        entityTextOffset: {
            x: 0,
            y: 0
        },

        isInvisible: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            if (!ig.textToCanvas || !(ig.TextLabelMixin || ig.TextToCanvasMixin)) {
                console.error('Text plugin/mixin is required for EntityText (ig.TextLabelMixin)');
                return;
            }

            if (settings && typeof settings.useOffscreenCanvas !== 'undefined') {
                this.useOffscreenCanvas = settings.useOffscreenCanvas;
            }

            // Initialize textConfig by merging defaults. The mixin handles the priority.
            this.initText(settings ? settings.textConfig : null);

            // Ensure overflowAllowance is a number
            this.textConfig.overflowAllowance = Number(this.textConfig.overflowAllowance) || 0;

            if (settings && settings.textOffset) {
                ig.merge(this.textOffset, settings.textOffset);
            }

            if (settings && typeof settings.alpha !== 'undefined') {
                this.alpha = settings.alpha;
            }
            if (settings && typeof settings.visualScale !== 'undefined') {
                this.visualScale = settings.visualScale;
            }

            if (this.useOffscreenCanvas) {
                this.initOffscreenCanvas();
            }
        },

        initOffscreenCanvas: function () {
            if (!this.useOffscreenCanvas) return;
            var hasFullSupport = typeof OffscreenCanvas !== 'undefined' &&
                                 typeof createImageBitmap !== 'undefined' &&
                                 typeof ImageBitmap !== 'undefined';

            var allowance = this.textConfig.overflowAllowance * 2; // Total extra width/height

            var canvasWidth = this.textConfig.width + allowance;
            var canvasHeight = this.textConfig.height + allowance;

            if (!hasFullSupport && typeof document !== 'undefined') {
                this.offCanvas = document.createElement("canvas");
            } else if (hasFullSupport) {
                this.offCanvas = new OffscreenCanvas(canvasWidth, canvasHeight);
            } else {
                this.useOffscreenCanvas = false; return;
            }
            if (!this.offCanvas) { this.useOffscreenCanvas = false; return; }

            this.offCanvas.width = canvasWidth;
            this.offCanvas.height = canvasHeight;
            this.offCanvasCTX = this.offCanvas.getContext("2d");
            if (this.offCanvasCTX.textRendering !== undefined) {
                this.offCanvasCTX.textRendering = "optimizeSpeed";
            }
        },

        redrawOffscreenCanvas: function () {
            if (!this.useOffscreenCanvas || !this.offCanvasCTX) return;

            var allowance = this.textConfig.overflowAllowance * 2;
            var newCanvasWidth = this.textConfig.width + allowance;
            var newCanvasHeight = this.textConfig.height + allowance;

            if (this.offCanvas.width !== newCanvasWidth || this.offCanvas.height !== newCanvasHeight) {
                this.offCanvas.width = newCanvasWidth;
                this.offCanvas.height = newCanvasHeight;
            }
            this.offCanvasCTX.clearRect(0, 0, this.offCanvas.width, this.offCanvas.height);
            this.drawTextToOffscreen();
            this.createImageBitmap();
            this.needsRedraw = false;
        },

        _applyShadowStyles: function (ctx) {
            if (this.textConfig.shadowEnabled) {
                ctx.shadowOffsetX = this.textConfig.shadowOffsetX || 0;
                ctx.shadowOffsetY = this.textConfig.shadowOffsetY || 0;
                ctx.shadowBlur = this.textConfig.shadowBlur || 0;
                ctx.shadowColor = this.textConfig.shadowColor || 'rgba(0,0,0,0)';
            }
        },

        _clearShadowStyles: function (ctx) {
            if (this.textConfig.shadowEnabled) {
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.shadowBlur = 0;
                ctx.shadowColor = 'rgba(0,0,0,0)';
            }
        },

        drawTextToOffscreen: function () {
             if (!this.textConfig || !this.textConfig.text || !ig.textToCanvas || !this.offCanvasCTX) return;

            // renderConfig uses logical width/height for text-to-canvas layout.
            // Text is drawn onto the larger off-screen canvas, offset by overflowAllowance.
            var renderConfig = {
                x: this.textConfig.overflowAllowance, // Draw text offset by allowance
                y: this.textConfig.overflowAllowance, // Draw text offset by allowance
                width: this.textConfig.width,         // Logical width for layout
                height: this.textConfig.height        // Logical height for layout
            };
            // Copy other textConfig properties
            for (var key in this.textConfig) {
                if (this.textConfig.hasOwnProperty(key) && !renderConfig.hasOwnProperty(key)) {
                    renderConfig[key] = this.textConfig[key];
                }
            }

            this.offCanvasCTX.save();
            this._applyShadowStyles(this.offCanvasCTX);

            ig.textToCanvas.drawText(this.offCanvasCTX, this.textConfig.text, renderConfig);

            this._clearShadowStyles(this.offCanvasCTX);
            this.offCanvasCTX.restore();
        },

        createImageBitmap: function () {
            var self = this;
            if (!self.useOffscreenCanvas || !self.offCanvas) return;
            if (self.imageBitmap && typeof self.imageBitmap.close === 'function') {
                self.imageBitmap.close(); self.imageBitmap = null;
            }
            if (typeof createImageBitmap !== 'undefined') {
                createImageBitmap(self.offCanvas)
                    .then(function (bitmap) { self.imageBitmap = bitmap; })
                    .catch(function (e) { console.error("EntityText: Error creating ImageBitmap.", e); self.imageBitmap = self.offCanvas; });
            } else { self.imageBitmap = self.offCanvas; }
        },

        _parseAnchorSpec: function (spec, defaultFactor) {
            defaultFactor = defaultFactor || { xFactor: 0, yFactor: 0 };
            if (typeof spec === 'string') {
                var s = spec.toLowerCase().replace(/\s+/g, '');
                var factors = { xFactor: 0.5, yFactor: 0.5 };
                if (s.indexOf('left') !== -1) factors.xFactor = 0; else if (s.indexOf('right') !== -1) factors.xFactor = 1;
                if (s.indexOf('top') !== -1) factors.yFactor = 0; else if (s.indexOf('bottom') !== -1) factors.yFactor = 1;
                if (s === 'top' || s === 'bottom') factors.xFactor = 0.5; if (s === 'left' || s === 'right') factors.yFactor = 0.5;
                if (s === 'top-left' || s === 'left-top') { factors.xFactor = 0; factors.yFactor = 0; }
                if (s === 'top-center' || s === 'center-top') { factors.xFactor = 0.5; factors.yFactor = 0; }
                if (s === 'top-right' || s === 'right-top') { factors.xFactor = 1; factors.yFactor = 0; }
                if (s === 'left-middle' || s === 'middle-left') { factors.xFactor = 0; factors.yFactor = 0.5; }
                if (s === 'center-middle' || s === 'middle-center' || s === 'center' || s === 'middle') { factors.xFactor = 0.5; factors.yFactor = 0.5; }
                if (s === 'right-middle' || s === 'middle-right') { factors.xFactor = 1; factors.yFactor = 0.5; }
                if (s === 'bottom-left' || s === 'left-bottom') { factors.xFactor = 0; factors.yFactor = 1; }
                if (s === 'bottom-center' || s === 'center-bottom') { factors.xFactor = 0.5; factors.yFactor = 1; }
                if (s === 'bottom-right' || s === 'right-bottom') { factors.xFactor = 1; factors.yFactor = 1; }
                return factors;
            } else if (spec && typeof spec.x === 'number' && typeof spec.y === 'number') {
                return { xFactor: (Math.max(-1, Math.min(1, spec.x)) + 1) / 2, yFactor: (Math.max(-1, Math.min(1, spec.y)) + 1) / 2 };
            }
            return defaultFactor;
        },

        /**
         * Anchors this text entity to another entity.
         * @param {ig.Entity} entity - The target entity to anchor to.
         * @param {Object} targetAnchorSpec - Anchor point on the target entity. E.g., {x: 0, y: 0} for center (-1 to 1 range).
         * @param {String|Object} [selfAlignOrPixelOffset] - If String: self-alignment keyword (e.g., "center-middle").
         * If Object: direct pixel offset {x,y}.
         * Defaults to "top-left" self-alignment if omitted.
         * @param {Object} [pixelOffsetIfSelfAlignString] - Optional pixel offset {x,y} ONLY if selfAlignOrPixelOffset was a string.
         */
        anchorTo: function (targetEntity, options) {
            if (!targetEntity) {
                console.error("EntityText.anchorTo: Target entity is null or undefined.");
                this.unanchor();
                return;
            }

            this._anchorTargetEntity = targetEntity;

            options = options || {};

            // Get targetAnchor from options, default to 'center-middle'
            // _parseAnchorSpec's second arg is the default if the first arg is invalid/missing
            this._targetAnchorFactor = this._parseAnchorSpec(options.targetAnchor, { xFactor: 0.5, yFactor: 0.5 });

            // Get selfAnchor from options, default to 'center-middle'
            this._selfAnchorFactor = this._parseAnchorSpec(options.selfAnchor, { xFactor: 0.5, yFactor: 0.5 });

            // Get offset from options, default to {x: 0, y: 0}
            if (options.offset && typeof options.offset.x === 'number' && typeof options.offset.y === 'number') {
                this._anchorPixelOffset = options.offset;
            } else {
                this._anchorPixelOffset = { x: 0, y: 0 };
            }

            this._isAnchored = true;
            this._updateAnchorPosition();
        },

        unanchor: function () {
            this._isAnchored = false;
            this._anchorTargetEntity = null;
        },

        _updateAnchorPosition: function () {
            if (!this._isAnchored || !this._anchorTargetEntity || this._anchorTargetEntity._killed) {
                if (this._anchorTargetEntity && this._anchorTargetEntity._killed) {
                    this.unanchor();
                }
                return;
            }

            var targetPointX = this._anchorTargetEntity.pos.x + (this._anchorTargetEntity.size.x * this._targetAnchorFactor.xFactor);
            var targetPointY = this._anchorTargetEntity.pos.y + (this._anchorTargetEntity.size.y * this._targetAnchorFactor.yFactor);

            var selfOffsetX = this.textConfig.width * this._selfAnchorFactor.xFactor;
            var selfOffsetY = this.textConfig.height * this._selfAnchorFactor.yFactor;

            this.pos.x = targetPointX - selfOffsetX + this._anchorPixelOffset.x;
            this.pos.y = targetPointY - selfOffsetY + this._anchorPixelOffset.y;
        },

        update: function () {
            if (this._isAnchored) {
                this._updateAnchorPosition();
            }
            this.parent();
        },

        draw: function () {
            if (this.alpha <= 0 || !this.textConfig || !this.textConfig.text || this.visualScale === 0) return;

            // Ensure anchor position is up-to-date even if update order causes a lag
            if (this._isAnchored) {
                this._updateAnchorPosition();
            }

            var ctx = ig.system.context;

            if (this.useOffscreenCanvas) {
                if (this.needsRedraw) { this.redrawOffscreenCanvas(); }

                if (!this.imageBitmap && this.offCanvas) { this.imageBitmap = this.offCanvas; } // Fallback
                if (!this.imageBitmap) { this.drawDirectly(ctx); return; } // Further fallback

                ctx.save();
                ctx.globalAlpha = this.alpha;

                // Determine pivotX_in_text_coords and pivotY_in_text_coords
                var pivotX_in_text_coords;
                var pivotY_in_text_coords;
                if (this._isAnchored && this._anchorTargetEntity) {
                    pivotX_in_text_coords = this.textConfig.width * this._selfAnchorFactor.xFactor;
                    pivotY_in_text_coords = this.textConfig.height * this._selfAnchorFactor.yFactor;
                } else {
                    pivotX_in_text_coords = this.textConfig.width / 2;
                    pivotY_in_text_coords = this.textConfig.height / 2;
                }

                // Calculate pivotScreenX and pivotScreenY
                var pivotScreenX = ig.system.getDrawPos(this.pos.x + pivotX_in_text_coords - ig.game.screen.x);
                var pivotScreenY = ig.system.getDrawPos(this.pos.y + pivotY_in_text_coords - ig.game.screen.y);

                // Translate and scale context
                ctx.translate(pivotScreenX, pivotScreenY);
                ctx.scale(this.visualScale, this.visualScale);

                // Draw imageBitmap
                ctx.drawImage(
                    this.imageBitmap,
                    -pivotX_in_text_coords - this.textConfig.overflowAllowance,
                    -pivotY_in_text_coords - this.textConfig.overflowAllowance
                );

                ctx.restore();
            } else {
                this.drawDirectly(ctx);
            }
        },

        drawDirectly: function (context) {
            context.save();
            context.globalAlpha = this.alpha;
            this._applyShadowStyles(context);
            var renderConfig = {};
            for (var key in this.textConfig) {
                if (this.textConfig.hasOwnProperty(key)) { renderConfig[key] = this.textConfig[key]; }
            }
            renderConfig.x = ig.system.getDrawPos(this.pos.x - ig.game.screen.x + this.textOffset.x);
            renderConfig.y = ig.system.getDrawPos(this.pos.y - ig.game.screen.y + this.textOffset.y);
            ig.textToCanvas.drawText(context, this.textConfig.text, renderConfig);
            this._clearShadowStyles(context);
            context.restore();
        },

        setPosition: function (x, y) {
            this.pos.x = x; this.pos.y = y;
        },


        updateAlpha: function (newAlpha) {
            var clampedAlpha = Math.max(0, Math.min(1, newAlpha));
            if (this.alpha === clampedAlpha) return;
            this.alpha = clampedAlpha;
        },

        kill: function () {
            if (this.imageBitmap && typeof this.imageBitmap.close === 'function') {
                this.imageBitmap.close(); this.imageBitmap = null;
            }
            this.offCanvas = null; this.offCanvasCTX = null;
            this.unanchor();
            this.parent();
        }
    });

    // Prefer the new TextLabelMixin. Keep backward compat with TextToCanvasMixin if present.
    if (ig.TextLabelMixin) {
        EntityText.inject(ig.TextLabelMixin);
    } else if (ig.TextToCanvasMixin) {
        // Backwards compatibility path (will be deprecated)
        EntityText.inject(ig.TextToCanvasMixin);
        if (!ig.__warnedTextToCanvasMixinOnce) {
            ig.__warnedTextToCanvasMixinOnce = true;
            if (console && console.warn) console.warn('TextToCanvasMixin is deprecated. Use ig.TextLabelMixin from plugins.utils.text.');
        }
    } else {
        console.error("No text mixin found for EntityText injection (expected ig.TextLabelMixin).");
    }

	    // Convenience: attach a text label entity to a target entity (inside module)
	    // options: { textConfig?, text?, offset?, zIndex?, alpha?, targetAnchor?, selfAnchor?, cache? }
	    ig.Text.attachLabel = function (target, options) {
	        options = options || {};
	        var cfg = options.textConfig ? (ig.copy ? ig.copy(options.textConfig) : JSON.parse(JSON.stringify(options.textConfig))) : (ig.copy ? ig.copy(ig.Text.defaults) : JSON.parse(JSON.stringify(ig.Text.defaults)));
	        if (typeof options.text !== 'undefined') { cfg.text = options.text; }
	        if (!cfg.width && target && target.size) { cfg.width = target.size.x; }
	        if (!cfg.height && target && target.size) { cfg.height = target.size.y; }

	        var useOffscreenCanvas = (typeof options.cache === 'undefined') ? true : !!options.cache;
	        var spawnX = target ? target.pos.x : 0;
	        var spawnY = target ? target.pos.y : 0;
	        var label = ig.game.spawnEntity(EntityText, spawnX, spawnY, {
	            textConfig: cfg,
	            alpha: (typeof options.alpha === 'number') ? options.alpha : (target && typeof target.alpha === 'number' ? target.alpha : 1),
	            zIndex: (typeof options.zIndex === 'number') ? options.zIndex : (target && typeof target.zIndex === 'number' ? (target.zIndex + 1) : 1),
	            useOffscreenCanvas: useOffscreenCanvas
	        });

	        if (label && target) {
	            label.anchorTo(target, {
	                targetAnchor: options.targetAnchor || { x: 0, y: 0 },
	                selfAnchor: options.selfAnchor || 'center-middle',
	                offset: options.offset || { x: 0, y: 0 }
	            });
	        }
	        return label;
	    };

});

