ig.module('game.entities.buttons.button-settings')
.requires(
    'plugins.utils.buttons.button-image',
    'game.entities.objects.popup-settings'
)
.defines(function () {
    "use strict";

    ig.EntityButtonSettings = ig.global.EntityButtonSettings = EntityButtonImage.extend({
        name: 'button-settings',
		idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/button/button-settings.png'), frameCountX: 1, frameCountY: 1 },
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;
            ig.game.sortEntitiesDeferred();
        },

        onClickCallback: function () {
            this._parent.tweenHide();
            var popup = ig.game.spawnEntity(EntityPopupSettingsMenu, -999, -999, {
                zIndex: ig.game.LAYERS.POPUP,
                _parent: this._parent
            });
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
