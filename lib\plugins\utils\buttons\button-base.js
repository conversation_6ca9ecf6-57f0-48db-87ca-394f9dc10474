ig.module('plugins.utils.buttons.button-base')
.requires(
    'plugins.utils.entity-extended',
    'plugins.utils.text.text'
)
.defines(function () {
    EntityButtonBase = ig.EntityExtended.extend({
        // Animation settings for the idle state
        idleSheetInfo: {
            sheetImage: new ig.Image('media/graphics/sprites/ui/button/button-blank.png'),
            frameCountX: 1,
            frameCountY: 1
        },
        _zIndex: 1, 

        // Button properties
        onClickCallback: function () {},
        buttonScale: 1,
        isClickable: true,
        STATES: {
            NORMAL: 0,
            CLICKED: 1
        },
        state: -1, 
        alpha: 1,
        hidden: false, // To control visibility

        // Configuration for the button's text label (to be passed to EntityText)
        buttonTextConfig: { // Renamed from entityTextConfig to avoid confusion
            fontSize: 32,
            fontFamily: 'Arial',
            fontColor: '#000000',
            text: 'Button', // Default text
            align: 'center',
            vAlign: 'middle',
            strokeColor: '#000000',
            strokeWidth: 0,
            justify: false
        },

        // Offset for positioning the EntityText relative to the button's center.
        buttonTextOffset: { // Renamed from entityTextOffset
            x: 0,
            y: 0
        },
        
        hasText: true, // Flag to indicate if this button should have a text label
        textEntity: null, // To store the instance of EntityText

        // Tweening properties for animations
        growTween: null,
        endGrowOnClick: true,

        singleClickOnly: false,
        hasBeenClicked: false,

        parentEntity: null,
        
        // Offscreen canvas properties for the button's BACKGROUND
        useOffscreenCanvas: true, 
        offCanvas: null,          
        offCanvasCTX: null,       
        imageBitmap: null,        
        needsRedraw: true,        // For the button's background
        
        init: function (x, y, settings) {
            this.zIndex = settings.zIndex || ig.game.LAYERS.UI; // Ensure zIndex is set

            if (settings && settings.useOffscreenCanvas !== undefined) {
                this.useOffscreenCanvas = settings.useOffscreenCanvas;
            }
            
            // Merge buttonTextConfig and buttonTextOffset from settings
            if (settings && settings.buttonTextConfig) {
                ig.merge(this.buttonTextConfig, settings.buttonTextConfig);
            }
            if (settings && settings.buttonTextOffset) {
                ig.merge(this.buttonTextOffset, settings.buttonTextOffset);
            }
    
            this.parent(x, y, settings); // Calls ig.Entity.init
    
            // Create the idle animation for the button background
            if (this.idleSheetInfo && this.idleSheetInfo.sheetImage) {
                 this.idleSheet = new ig.AnimationSheet(this.idleSheetInfo.sheetImage.path, this.size.x, this.size.y); // Use button's size for anim sheet
                 this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
                 this.currentAnim = this.idle;
            } else {
                console.warn("EntityButtonBase: idleSheetInfo or sheetImage not provided. Button background may not render.");
            }
    
            this.state = this.STATES.NORMAL;
            
            // Initialize and attach a label for the button with a single helper
            if (this.hasText) {
                this.textEntity = ig.Text.attachLabel(this, {
                    textConfig: this.buttonTextConfig,
                    offset: this.buttonTextOffset,
                    zIndex: this.zIndex + 1,
                    alpha: this.alpha,
                    cache: true
                });
            }

            this.onClick = new ig.Signal();
            if (this.onClickCallback && typeof this.onClickCallback === 'function') {
                this.onClick.add(this.onClickCallback, this);
            }
            
            // Initialize the offscreen canvas for the button's BACKGROUND
            if (this.useOffscreenCanvas) {
                this.initOffscreenCanvas();
            }
            
            ig.game.sortEntitiesDeferred();
        },
        
        /**
         * Initialize the offscreen canvas for the button's BACKGROUND.
         */
        initOffscreenCanvas: function() {
            if (!this.useOffscreenCanvas) return;

            var hasFullSupport = typeof OffscreenCanvas !== 'undefined' && 
                                 typeof createImageBitmap !== 'undefined' &&
                                 typeof ImageBitmap !== 'undefined';
            
            if (!hasFullSupport && typeof document !== 'undefined') {
                this.offCanvas = document.createElement("canvas");
            } else if (hasFullSupport) {
                this.offCanvas = new OffscreenCanvas(this.size.x, this.size.y);
            } else {
                this.useOffscreenCanvas = false;
                return;
            }

            if (!this.offCanvas) {
                 this.useOffscreenCanvas = false;
                 return;
            }
            
            this.offCanvas.width = this.size.x;
            this.offCanvas.height = this.size.y;
            this.offCanvasCTX = this.offCanvas.getContext("2d");
            
            // Initial draw to the offscreen canvas for the button background
            this.redrawOffscreenCanvas(); 
        },
        
        /**
         * Redraws the button's BACKGROUND to its offscreen canvas.
         */
        redrawOffscreenCanvas: function() {
            if (!this.useOffscreenCanvas || !this.offCanvasCTX) return;
            
            this.offCanvasCTX.clearRect(0, 0, this.offCanvas.width, this.offCanvas.height);
            this.drawButtonToOffscreen(); // Only draws the button's own graphics (background)
            this.createImageBitmapFromButton(); // Creates bitmap for the button's background
            
            this.needsRedraw = false; // For the button's background
        },
        
        /**
         * Draw the button's BACKGROUND sprite to its offscreen canvas.
         */
        drawButtonToOffscreen: function() {
            if (!this.currentAnim || !this.currentAnim.sheet || !this.currentAnim.sheet.image || !this.offCanvasCTX) return;
            
            var frame = this.currentAnim.sheet.image;
            // Assuming currentAnim.tile is the correct index for the frame
            var tile = this.currentAnim.tile; 
            var sheet = this.currentAnim.sheet;
            var tileWidth = sheet.width;
            var tileHeight = sheet.height;
            var sX = (tile % this.idleSheetInfo.frameCountX) * tileWidth;
            var sY = (tile % this.idleSheetInfo.frameCountY) * tileHeight;
            
            this.offCanvasCTX.drawImage(
                frame.data, 
                sX, sY, tileWidth, tileHeight, // Source rectangle from the animation sheet
                0, 0, this.size.x, this.size.y // Destination rectangle on the off-screen canvas
            );
        },
        
        /**
         * Creates an ImageBitmap from the button's offscreen canvas.
         */
        createImageBitmapFromButton: function() { // Renamed for clarity
            var self = this;
            if (!self.useOffscreenCanvas || !self.offCanvas) return;

            if (self.imageBitmap && typeof self.imageBitmap.close === 'function') {
                self.imageBitmap.close();
                self.imageBitmap = null;
            }
            
            if (typeof createImageBitmap !== 'undefined') {
                createImageBitmap(self.offCanvas)
                    .then(function(bitmap) {
                        self.imageBitmap = bitmap;
                    })
                    .catch(function(e) {
                        console.error("EntityButtonBase: Error creating ImageBitmap for button background.", e);
                        self.imageBitmap = self.offCanvas; // Fallback
                    });
            } else {
                self.imageBitmap = self.offCanvas;
            }
        },

        /**
         * Updates the text displayed on the button's label.
         * @param {string} text - The new text to display.
         */
        updateButtonLabelText: function (text) {
            if (this.textEntity) {
                this.textEntity.setTextContent(text);
            } else if (this.hasText) { // If textEntity somehow wasn't created but should exist
                console.warn("EntityButtonBase: Attempted to update text, but textEntity is missing.");
                // Optionally, update buttonTextConfig and mark for potential recreation if needed
                this.buttonTextConfig.text = text;
            }
        },
        
        /**
         * Updates the configuration of the button's text label.
         * @param {Object} config - New configuration properties for the EntityText.
         */
        updateButtonLabelConfig: function(config) {
            if (this.textEntity) {
                this.textEntity.configureText(config);
            } else if (this.hasText) {
                 console.warn("EntityButtonBase: Attempted to configure text, but textEntity is missing.");
                 ig.merge(this.buttonTextConfig, config); // Store for potential future use
            }
        },
        
        /**
         * Sets the scale of the button's text label.
         * @param {Number} scale - Scale factor.
         */
        setButtonLabelScale: function(scale) {
            if (this.textEntity) {
                this.textEntity.setTextScale(scale);
            }
        },

        clicked: function () {
            if (this.disabled || (this.singleClickOnly && this.hasBeenClicked)) return;
            if (this.parentEntity && this.parentEntity.isTweening) return;

            if (this.growTween) {
                if (this.endGrowOnClick) {
                    this.growTween.stop();
                    this.growTween = null;
                } else {
                    this.growTween.pause();
                }
            }

            this.state = this.STATES.CLICKED;
            this.buttonScale = 0.98;
            this.updateButtonVisualScale();
        },

        released: function () {
            if (this.state !== this.STATES.CLICKED) return;
            if (this.disabled || (this.singleClickOnly && this.hasBeenClicked)) return;
            if (this.parentEntity && this.parentEntity.isTweening) return;

            if (this.growTween) {
                var self = this;
                this.delayedCall(0.5, function () {
                    if (self.growTween) self.growTween.resume();
                });
            }

            if (ig.soundHandler.sfxPlayer.soundList.click) {
                ig.soundHandler.sfxPlayer.play('click');
            }

            this.state = this.STATES.NORMAL;
            this.buttonScale = 1;
            this.updateButtonVisualScale();

            var self = this;
            this.tween({}, 0.01, { // Minimal tween for delayed callback
                onComplete: function () {
                    if (self.onClick) self.onClick.dispatch(self); // Pass button instance
                    if (self.singleClickOnly) {
                        self.hasBeenClicked = true;
                        self.disable();
                    }
                }
            }).start();
        },

        releasedOutside: function () {
            this.state = this.STATES.NORMAL;
            this.buttonScale = 1;
            this.updateButtonVisualScale();
        },

        hide: function () {
            if (this.hidden) return;
            this.disable();
            this.hidden = true;
            // No need to change zIndex if we control alpha and clickability
            this.alpha = 0; 
            if (this.textEntity) {
                this.textEntity.updateAlpha(0);
            }
        },

        show: function () {
            if (!this.hidden) return;
            this.enable();
            this.hidden = false;
            this.alpha = 1; 
             if (this.textEntity) {
                this.textEntity.updateAlpha(1);
            }
        },

        disable: function () {
            if (this.disabled) return;
            this.disabled = true;
            this.isClickable = false; // Explicitly make not clickable
            if (this.growTween) this.growTween.pause();
        },

        enable: function () {
            if (!this.disabled) return;
            this.disabled = false;
            this.isClickable = true; // Explicitly make clickable
            if (this.growTween) this.growTween.resume();
        },

        grow: function (isInfinite, speed) {
            var repeat = isInfinite ? Infinity : 0; // 0 for once, Infinity for loop
            speed = speed || 200;
            var orig = { scale: 1 };
            var self = this; // For ES5 'this'

            this.growTween = new ig.TweenDef(orig)
                .easing(ig.Tween.Easing.Back.EaseOut)
                .to({ scale: 1.075 }, speed)
                .repeat(repeat)
                .yoyo(true)
                .onUpdate(function () {
                    self.buttonScale = orig.scale;
                    self.updateButtonVisualScale();
                })
                .start();
        },

        /**
         * Updates the button's visual scale (background and text label).
         */
        updateButtonVisualScale: function () {
            // Text label scale is handled by EntityText's own scaling,
            // but if we want the button's scale to drive text scale:
            if (this.textEntity) {
                 this.textEntity.setTextScale(this.buttonScale);
            }
            
            // For the button's background, scaling is applied in the draw method
            // if using offscreen canvas. If not, scale currentAnim directly.
            if (!this.useOffscreenCanvas && this.currentAnim) {
                this.currentAnim.scale.x = this.buttonScale;
                this.currentAnim.scale.y = this.buttonScale;
            }
            // If using offscreen canvas, this.buttonScale is used in the draw() method.
            // No need to redraw the offscreen canvas just for a scale change,
            // as the bitmap is drawn scaled.
        },
        
        updateAlpha: function(newAlpha) {
            this.alpha = newAlpha;
            if (this.textEntity) {
                this.textEntity.updateAlpha(newAlpha);
            }
            // If not using offscreen canvas for button bg, update anim alpha
            if (!this.useOffscreenCanvas && this.currentAnim) {
                this.currentAnim.alpha = newAlpha;
            }
            // If using offscreen canvas, globalAlpha is applied in draw method.
        },

        onDrop: function () { // From Draggable mixin if used
            this.releasedOutside();
            this.parent();
        },

        draw: function () {
            if (this.hidden || !this.currentAnim || this.alpha <= 0) return;
            
            var ctx = ig.system.context;

            if (this.useOffscreenCanvas && this.imageBitmap) {
                if (this.needsRedraw) { // For button background
                    this.redrawOffscreenCanvas();
                }
                 // Ensure imageBitmap is ready (createImageBitmapFromButton is async)
                if (!this.imageBitmap && this.offCanvas) { 
                    this.imageBitmap = this.offCanvas; 
                }
                if (!this.imageBitmap) { // Still no bitmap, try direct draw of anim
                     if (this.currentAnim) this.currentAnim.draw(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
                     return; // Text entity will draw itself
                }

                ctx.save();
                ctx.globalAlpha = this.alpha;
                
                var x = this.pos.x - ig.game.screen.x;
                var y = this.pos.y - ig.game.screen.y;
                
                var drawX = ig.system.getDrawPos(x);
                var drawY = ig.system.getDrawPos(y);
                
                var centerX = drawX + this.size.x / 2;
                var centerY = drawY + this.size.y / 2;
                
                ctx.translate(centerX, centerY);
                ctx.scale(this.buttonScale, this.buttonScale); // Apply button's visual scale
                ctx.translate(-this.size.x / 2, -this.size.y / 2); // Translate back from center for drawing
                
                ctx.drawImage(this.imageBitmap, 0, 0); // Draw the button's background bitmap
                
                ctx.restore();
            } else {
                // Traditional drawing for button background (if not using offscreen canvas)
                // The parent() call for ig.Entity doesn't draw. We need to draw currentAnim.
                 if (this.currentAnim) {
                    this.currentAnim.alpha = this.alpha; // Ensure anim alpha is up-to-date
                    this.currentAnim.draw(
                        this.pos.x - ig.game.screen.x,
                        this.pos.y - ig.game.screen.y
                    );
                }
            }
            // The textEntity will draw itself independently in the game loop.
        },

        update: function () {
            this.parent(); // Handles basic entity updates like animation

            if (ig.input.released('click') && this.state === this.STATES.CLICKED && !this.underPointer()) {
                this.releasedOutside();
            }

            if (this.growTween) {
                if (this.underPointer()) {
                    if (this.growTween._isPlaying) {
                        this.growTween.pause();
                        this.buttonScale = 1;
                        this.updateButtonVisualScale();
                    }
                } else {
                    if (!this.growTween._isPlaying && !this.disabled) this.growTween.resume();
                }
            }
            
            // Update text entity's alpha if button's alpha changed by other means
            if (this.textEntity && this.textEntity.alpha !== this.alpha) {
                this.textEntity.updateAlpha(this.alpha);
            }
        },
        
        kill: function() {
            if (this.imageBitmap && typeof this.imageBitmap.close === 'function') {
                this.imageBitmap.close();
                this.imageBitmap = null;
            }
            this.offCanvas = null;
            this.offCanvasCTX = null;

            if (this.textEntity) {
                this.textEntity.kill();
                this.textEntity = null;
            }
            
            this.parent();
        }
    });
});
