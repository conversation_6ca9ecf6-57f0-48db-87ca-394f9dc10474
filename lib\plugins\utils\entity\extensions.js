ig.module('plugins.utils.entity.extensions')
.requires(
    'impact.entity',
    'plugins.tween'
)
.defines(function () { 'use strict';

    /*
     * Extra properties for ig.Timer.
     */
    ig.Timer.minStep = (1000 / 60) / 1000;
    ig.Timer.overflow = 0;
    ig.Timer.stepped = false;

    /**
     * Delays a function call by a certain duration.
     * @param {number} duration Duration in seconds.
     * @param {function} onComplete Function to call when the duration is up.
     * @param {boolean} [autoStart=true] Set to false if you want to start the tween later.
     * @returns {ig.Tween} The tween instance that was created.
     */
    ig.Entity.prototype.delayedCall = function (duration, onComplete, autoStart) {
        if (autoStart === undefined) {
            autoStart = true;
        }
        var tween = new ig.Tween(this, {}, duration, { onComplete: onComplete });
        this.tweens.push(tween);
        if (autoStart) {
            tween.start();
        }
        return tween;
    };
});
