ig.module('plugins.utils.math.geometry')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    /**
     * Calculates the distance between two points.
     * @param {Object} p1 First point with x and y properties
     * @param {Object} p2 Second point with x and y properties
     * @returns {Number} Distance between p1 and p2
     */
    ig.utils.distanceBetweenPoints = function (p1, p2) {
        return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
    };

    /**
     * Calculates the distance between two coordinates.
     * @param {Number} x1 X-coordinate of first point
     * @param {Number} y1 Y-coordinate of first point
     * @param {Number} x2 X-coordinate of second point
     * @param {Number} y2 Y-coordinate of second point
     * @returns {Number} Distance between the two points
     */
    ig.utils.distanceBetween = function (x1, y1, x2, y2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
    };

    /**
     * Calculates the angle between two points.
     * @param {Object} p1 First point with x and y properties
     * @param {Object} p2 Second point with x and y properties
     * @returns {Number} Angle in radians between p1 and p2
     */
    ig.utils.angleBetweenPoints = function (p1, p2) {
        return Math.atan2(p2.y - p1.y, p2.x - p1.x);
    };

    /**
     * Calculates the angle between two coordinates.
     * @param {Number} x1 X-coordinate of first point
     * @param {Number} y1 Y-coordinate of first point
     * @param {Number} x2 X-coordinate of second point
     * @param {Number} y2 Y-coordinate of second point
     * @returns {Number} Angle in radians between the two points
     */
    ig.utils.angleBetween = function (x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    };

    /**
     * Rotate an object around on another object
     * @param   {object}  obj  - x, y position
     * @param   {number}  angle - angle to rotate
     * @param   {number}  distance - distance between 2 objects
     * @return  {Vector2} returns new position
     */
    ig.utils.rotateAround = function (obj, angle, distance){
        var theta = angle * Math.PI/180;
        var newX = distance * Math.cos(theta) + obj.x;
        var newY = distance * Math.sin(theta) + obj.y;
        return { x: newX, y: newY };
    };

    ig.utils.rotatePoint = function (pointX, pointY, centerX, centerY, angle) {
        // Standard 2D rotation formula
        var translatedX = pointX - centerX;
        var translatedY = pointY - centerY;
        var rotatedX = translatedX * Math.cos(angle) - translatedY * Math.sin(angle);
        var rotatedY = translatedX * Math.sin(angle) + translatedY * Math.cos(angle);
        return {
            x: rotatedX + centerX,
            y: rotatedY + centerY
        };
    };

    ig.utils.getVertices = function (originX, originY, angle, width, height) {
        var s = Math.sin(angle),
            c = Math.cos(angle),
            halfW = width / 2,
            halfH = height / 2;

        var vertices = [{
            x: originX - halfH * s - halfW * c,
            y: originY - halfH * c + halfW * s
        }, {
            x: originX - halfH * s + halfW * c,
            y: originY - halfH * c - halfW * s
        }, {
            x: originX + halfH * s + halfW * c,
            y: originY + halfH * c - halfW * s
        }, {
            x: originX + halfH * s - halfW * c,
            y: originY + halfH * c + halfW * s
        }];

        return vertices;
    };

    ig.utils.distanceFromPointToLine = function (point, vertice1, vertice2) {
        var x = point.x,
            y = point.y,
            x1 = vertice1.x,
            y1 = vertice1.y,
            x2 = vertice2.x,
            y2 = vertice2.y;
        var A = x - x1;
        var B = y - y1;
        var C = x2 - x1;
        var D = y2 - y1;

        var dot = A * C + B * D;
        var len_sq = C * C + D * D;
        var param = -1;
        if (len_sq != 0) //in case of 0 length line
            param = dot / len_sq;

        var xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        var dx = x - xx;
        var dy = y - yy;
        return Math.sqrt(dx * dx + dy * dy);
    };

    /**
     * Determines if a point is contained within a polygon defined by vertices.
     * @param {Array} vertices Array of points (objects with x and y)
     * @param {Object} point Point with x and y properties
     * @returns {Boolean} True if point is inside the polygon, else false
     */
    ig.utils.containPoint = function (vertices, point) {
        var check;
        for (var i = 0; i < vertices.length; i++) {
            var j = i + 1;
            if (j == vertices.length) j = 0;
            var p0 = vertices[i],
                p1 = vertices[j];
            var side = (point.y - p0.y) * (p1.x - p0.x) - (point.x - p0.x) * (p1.y - p0.y);
            if (side == 0) return true;
            if (i === 0) check = side;
            else if (check * side < 0) return false;
        }
        return true;
    };

    /**
     * Check if a point is inside the polygon
     * (used for mouse clicking detection)
     */
    ig.utils.pointInPolygon = function (point, polygon) {
        if (!polygon || polygon.length < 3) return false;

        var inside = false;
        var j = polygon.length - 1;

        for (var i = 0; i < polygon.length; i++) {
            if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
                (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) /
                (polygon[j].y - polygon[i].y) + polygon[i].x)) {
                inside = !inside;
            }
            j = i;
        }

        return inside;
    };

    ig.utils.calculatePolygonCenter = function (vertices) {
        if (!vertices || vertices.length === 0) return { x: 0, y: 0 };
        var sumX = 0, sumY = 0;
        for (var i = 0; i < vertices.length; i++) {
            sumX += vertices[i].x;
            sumY += vertices[i].y;
        }
        return { x: sumX / vertices.length, y: sumY / vertices.length };
    };
});
