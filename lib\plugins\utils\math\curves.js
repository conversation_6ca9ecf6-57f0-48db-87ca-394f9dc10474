ig.module('plugins.utils.math.curves')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    ig.utils.createQuadraticBezierCurve = function (p0, p1, p2, numPoints) {
        var points = [];
        for (var i = 1; i < numPoints; i++) {
            var t = i / numPoints;
            var u = 1 - t;
            points.push({
                x: u * u * p0.x + 2 * u * t * p1.x + t * t * p2.x,
                y: u * u * p0.y + 2 * u * t * p1.y + t * t * p2.y
            });
        }
        return points;
    };

    ig.utils.createCubicBezierCurve = function (p0, p1, p2, p3, numPoints) {
        var points = [];
        for (var i = 1; i < numPoints; i++) {
            var t = i / numPoints;
            var u = 1 - t;
            var tt = t * t;
            var uu = u * u;
            var uuu = uu * u;
            var ttt = tt * t;
            
            points.push({
                x: uuu * p0.x + 3 * uu * t * p1.x + 3 * u * tt * p2.x + ttt * p3.x,
                y: uuu * p0.y + 3 * uu * t * p1.y + 3 * u * tt * p2.y + ttt * p3.y
            });
        }
        return points;
    };
});
