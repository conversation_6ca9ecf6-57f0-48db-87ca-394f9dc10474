ig.module('game.entities.buttons.button-restart')
.requires(
    'plugins.utils.buttons.button-image'
)
.defines(function () {
    "use strict";

    ig.EntityButtonRestart = ig.global.EntityButtonRestart = EntityButtonImage.extend({
        name: 'button-restart',
		idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/button/button-restart.png'), frameCountX: 1, frameCountY: 1 },
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;
            ig.game.sortEntitiesDeferred();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();
        }
    });
});
