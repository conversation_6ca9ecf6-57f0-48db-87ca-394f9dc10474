ig.module('plugins.utils.entity.types')
.requires(
    'impact.entity'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};
    var MAX_TYPE = Math.pow(2, 32);

    /**
     * Gets a type flag by space separated names, and if does not exists, creates it. In ImpactJS, there are 3 default types: A, B, and BOTH. This system allows you to build up to 32 types for much finer control over type, checkAgainst, and group. If you don't understand bitflags, a quick google search will be very helpful!
     *  to avoid poor performance, only use this when initializing an entity
     * IMPORTANT: maxes out at 32 types/flags due to the way Javascript handles bitwise operations.
     * @param {Class} classObject class / object to record types in.
     * @param {String} names space separated names to create type for.
     * @param {String} [typeListName] list name to store types in, defaults to TYPE.
     * @see ig.utils.addType
     **/
    ig.utils.getType = function (classObject, names, typeListName) {
        typeListName = typeListName || 'TYPE';
        var types = classObject[typeListName];
        var typeLastName = typeListName + "_LAST";
        var type;

        // setup types
        if (!classObject[typeLastName] || !types) {
            classObject[typeLastName] = 1;
            types = classObject[typeListName] = {};
        }

        // get type
        names = names.toUpperCase();
        type = types[names];

        // create type
        if (!type) {
            type = 0;
            var typeLast = classObject[typeLastName];
            var namesList = names.split(" ");
            for (var i = 0, il = namesList.length; i < il; i++) {
                var name = namesList[i];
                var typeNext = types[name];
                if (!typeNext) {
                    if (typeLast >= MAX_TYPE) {
                        throw new TypeError('Bitwise flag out of range / above 32 bits!');
                    }
                    // these types are bitwise flags
                    // multiply last type by 2 each time to avoid false positives
                    typeNext = types[name] = typeLast;
                    classObject[typeLastName] = typeLast * 2;
                }
                // add to total type
                type |= typeNext;
            }
            // lets not recalculate that again
            types[names] = type;
        }

        return type;
    };

    /**
     * Adds space separated type flags by name to a property of an entity. In ImpactJS, there are 3 default types: A, B, and BOTH. This system allows you to build up to 32 types for much finer control over type, checkAgainst, and group. If you don't understand bitflags, a quick google search will be very helpful!
     *  to avoid poor performance, only use this when initializing an entity
     * IMPORTANT: maxes out at 32 types/flags due to the way Javascript handles bitwise operations.
     * @param {Class} classObject class object to record types in.
     * @param {ig.EntityExtended} entity entity to add types to.
     * @param {String} property property name within entity to add types to.
     * @param {String} names space separated names to create type for.
     * @param {String} [typeListName] list name to store types in, defaults to TYPE.
     * @example
     *      spawn an entity
     *          var entity = ig.game.spawnEntity( ig.Entity, 0, 0, { ...settings...} );
     *      add a type to our new entity
     *      this type system defaults to using TYPE as the namespace to record unique types
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_NAME" );
     *      when we need to do dynamic checks for that type
     *      refer to it directly using the TYPE namespace
     *          ig.Entity.TYPE.TYPE_NAME
     *      and a check against the type of our new entity will return truthy
     *          entity.type & ig.Entity.TYPE.TYPE_NAME
     *      we can also add a GROUP type to our new entity
     *      note the last argument, we're using the GROUP namespace
     *      this is because group types should be different from TYPE types
     *          ig.utils.addType( ig.Entity, entity, "group", "GROUP_NAME", "GROUP" );
     *      refer to it directly using the GROUP namespace
     *          ig.Entity.GROUP.GROUP_NAME
     *      remember, types and groups can be added together to make combinations!
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_1" );
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_2" );
     *          ig.utils.addType( ig.Entity, entity, "type", "TYPE_3" );
     *      and we can still check for any one or more of those types easily
     *          entity.type & ig.Entity.TYPE.TYPE_1 === truthy
     *          entity.type & ( ig.Entity.TYPE.TYPE_1 | ig.Entity.TYPE.TYPE_2 ) === truthy
     *          entity.type & ig.Entity.TYPE.TYPE_4 === falsy
     **/
    ig.utils.addType = function (classObject, entity, property, names, typeListName) {
        entity[property] |= ig.utils.getType(classObject, names, typeListName);
    };
});

