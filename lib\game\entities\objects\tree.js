ig.module('game.entities.objects.tree')
.requires(
    'plugins.utils.entity-extended'
)
.defines(function () {
    EntityTree = ig.EntityExtended.extend({
        zIndex: 0,
        name: 'tree',

        // Tree animation properties
        boolAnimShake: false,
        alphaTree: 1,
        offsetTree: { x: 0, y: 0 },
        scaleLeaf: 1,

        // Leaf management
        maxLeaves: 500,
        totalLeaves: 0,
        lastLeaves: 0,

        // Tree and leaf images
        imgLeaf1: new ig.Image('media/graphics/sprites/game/leaf1.png'),
        imgLeaf2: new ig.Image('media/graphics/sprites/game/leaf2.png'),
        imgLeaf3: new ig.Image('media/graphics/sprites/game/leaf3.png'),
        imgLeaf4: new ig.Image('media/graphics/sprites/game/leaf4.png'),
        imgGrass: new ig.Image('media/graphics/sprites/game/grass.png'),
        imgTree: new ig.Image('media/graphics/sprites/game/tree.png'),

        // Tree data - leaf types (1-4 representing different leaf sprites)
        typeLeaves: null,
        // Tree data - leaf flip directions (1 for normal, -1 for flipped)
        flipLeaves: null,
        // Tree data - leaf positions with x, y coordinates and rotation angles
        // This will be populated from the original _DATAGAME.posLeaves
        posLeaves: [],

        isClickable: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            // Copy tree data from ig.GameData if it exists
            if (typeof ig.GameData !== 'undefined') {
                if (ig.GameData.posLeaves) {
                    this.posLeaves = ig.GameData.posLeaves;
                }
                if (ig.GameData.typeLeaves) {
                    this.typeLeaves = ig.GameData.typeLeaves;
                }
                if (ig.GameData.flipLeaves) {
                    this.flipLeaves = ig.GameData.flipLeaves;
                }
            }

            this.ctx = ig.system.context;

            this.size.x = this.imgTree.width;
            this.size.y = this.imgTree.height;
        },

        clicked: function () {
            this.setLeafCount(this.totalLeaves + 1);
        },

        setLeafCount: function (count) {
            var newCount = Math.min(count, this.maxLeaves);
            if (newCount > this.totalLeaves) {
                this.totalLeaves = newCount;
                this.treeShakeEffect();
            } else {
                this.totalLeaves = newCount;
            }
        },

        treeShakeEffect: function () {
            if (this.boolAnimShake) return;

            var durShake = 0.1;
            var durPulse = 0.2;
            var durScale = 0.2;

            var finishPulse = false;
            var finishShake = false;
            var finishScale = false;

            this.scaleLeaf = 0;
            this.boolAnimShake = true;

            // Scale animation for new leaves
            this.tweenScale = this.tween({
                scaleLeaf: 1
            }, durPulse, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    finishScale = true;
                    if (finishPulse && finishShake && finishScale) {
                        this.boolAnimShake = false;
                        this.lastLeaves = this.totalLeaves;
                    }
                }.bind(this)
            });
            this.tweenScale.start();

            // Pulse animation
            this.tweenPulseOut = this.tween({
                alphaTree: 0.5
            }, durPulse, {
                easing: ig.Tween.Easing.Linear.EaseNone
            });

            this.tweenPulseIn = this.tween({
                alphaTree: 1
            }, durPulse, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    finishPulse = true;
                    if (finishPulse && finishShake && finishScale) {
                        this.boolAnimShake = false;
                        this.lastLeaves = this.totalLeaves;
                    }
                }.bind(this)
            });

            this.tweenPulseOut.chain(this.tweenPulseIn);
            this.tweenPulseOut.start();

            // Shake animation
            this.tweenShakeLeft = this.tween({
                offsetTree: { x: -5 }
            }, durShake, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    this.tweenShakeCenter1.start();
                }.bind(this)
            });

            this.tweenShakeRight = this.tween({
                offsetTree: { x: 5 }
            }, durShake, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    this.tweenShakeCenter2.start();
                }.bind(this)
            });

            this.tweenShakeCenter1 = this.tween({
                offsetTree: { x: 0 }
            }, durShake, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    this.tweenShakeRight.start();
                }.bind(this)
            });

            this.tweenShakeCenter2 = this.tween({
                offsetTree: { x: 0 }
            }, durShake, {
                easing: ig.Tween.Easing.Linear.EaseNone,
                onComplete: function () {
                    finishShake = true;
                    if (finishPulse && finishShake && finishScale) {
                        this.boolAnimShake = false;
                        this.lastLeaves = this.totalLeaves;
                    }
                }.bind(this)
            });

            this.tweenShakeLeft.start();
        },

        drawTree: function () {

            // Draw grass
            this.imgGrass.draw(ig.game.midX - this.imgGrass.width / 2 - 5, ig.system.height - this.imgGrass.height);

            // Calculate tree position with offset
            var treeX = this.pos.x + this.offsetTree.x - ig.game.screen.x;
            var treeY = this.pos.y + this.offsetTree.y - ig.game.screen.y;
            // Draw tree trunk
            this.ctx.save();
            this.ctx.globalAlpha = this.alphaTree;
            this.imgTree.draw(treeX, treeY);
            this.ctx.restore();

            // Draw leaves
            for (var leaf = 1; leaf <= this.totalLeaves; leaf++) {
                this.ctx.save();
                this.ctx.globalAlpha = this.alphaTree;

                var typeL = this.typeLeaves[leaf];
                var posL = this.posLeaves[leaf];
                var flipL = this.flipLeaves[leaf];

                if (!posL) continue; // Skip if position data is missing

                this.ctx.translate(treeX + posL.x, treeY + posL.y);
                this.ctx.rotate(posL.angle * Math.PI / 180);

                if (leaf > this.lastLeaves) {
                    this.ctx.scale(flipL * this.scaleLeaf, this.scaleLeaf);
                } else {
                    this.ctx.scale(flipL, 1);
                }

                var leafImg = this['imgLeaf' + typeL];
                if (leafImg) {
                    if (typeL == 2 || typeL == 4) {
                        leafImg.draw((-leafImg.width / 2) - 4, -leafImg.height);
                    } else {
                        leafImg.draw(-leafImg.width / 2, -leafImg.height);
                    }
                }
                this.ctx.restore();
            }
        },

        draw: function () {
            this.parent();
            this.drawTree();
        },

        update: function () {
            this.parent();
            this.pos.x = ig.system.width * 0.5 + ig.game.screen.x - this.imgTree.width * 0.5;
            this.pos.y = ig.system.height + ig.game.screen.y - this.imgGrass.height - this.imgTree.height + 55;
        }
    });
});
