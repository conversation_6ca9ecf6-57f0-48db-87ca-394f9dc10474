ig.module('game.entities.objects.tree')
.requires(
    'plugins.utils.entity-extended'
)
.defines(function () {
    EntityTree = ig.EntityExtended.extend({
        zIndex: 0,
        name: 'tree',

        // Tree animation properties
        boolAnimShake: false,
        alphaTree: 1,
        offsetTree: { x: 0, y: 0 },
        scaleLeaf: 1,

        // Rotation-based shake properties
        rotationAngle: 0, // Current rotation angle in radians
        maxRotationDegrees: 2, // Maximum rotation angle in degrees (configurable)

        // Animation spamming support
        shakeIntensity: 0, // Accumulated shake intensity for spamming
        pendingShakes: 0, // Number of pending shake requests
        activeTweens: [], // Track active tweens for cleanup

        // Leaf management
        maxLeaves: 500,
        totalLeaves: 0,
        lastLeaves: 0,

        // Tree and leaf images
        imgLeaf1: new ig.Image('media/graphics/sprites/game/leaf1.png'),
        imgLeaf2: new ig.Image('media/graphics/sprites/game/leaf2.png'),
        imgLeaf3: new ig.Image('media/graphics/sprites/game/leaf3.png'),
        imgLeaf4: new ig.Image('media/graphics/sprites/game/leaf4.png'),
        imgGrass: new ig.Image('media/graphics/sprites/game/grass.png'),
        imgTree: new ig.Image('media/graphics/sprites/game/tree.png'),

        // Tree data - leaf types (1-4 representing different leaf sprites)
        typeLeaves: null,
        // Tree data - leaf flip directions (1 for normal, -1 for flipped)
        flipLeaves: null,
        // Tree data - leaf positions with x, y coordinates and rotation angles
        // This will be populated from the original _DATAGAME.posLeaves
        posLeaves: [],

        isClickable: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            // Copy tree data from ig.GameData if it exists
            if (typeof ig.GameData !== 'undefined') {
                if (ig.GameData.posLeaves) {
                    this.posLeaves = ig.GameData.posLeaves;
                }
                if (ig.GameData.typeLeaves) {
                    this.typeLeaves = ig.GameData.typeLeaves;
                }
                if (ig.GameData.flipLeaves) {
                    this.flipLeaves = ig.GameData.flipLeaves;
                }
            }

            this.ctx = ig.system.context;

            this.size.x = this.imgTree.width;
            this.size.y = this.imgTree.height;
        },

        released: function () {
            this.setLeafCount(this.totalLeaves + 1);
        },

        setLeafCount: function (count) {
            var newCount = Math.min(count, this.maxLeaves);
            if (newCount > this.totalLeaves) {
                this.totalLeaves = newCount;
                this.treeShakeEffect();
            } else {
                this.totalLeaves = newCount;
            }
        },

        treeShakeEffect: function () {
            // Increment pending shakes and intensity
            this.pendingShakes++;
            this.shakeIntensity = Math.min(this.shakeIntensity + 0.3, 2.0); // Cap intensity at 2x

            // If already animating, the current animation will handle the accumulated intensity
            if (this.boolAnimShake) {
                return;
            }

            this._startShakeAnimation();
        },

        _startShakeAnimation: function () {
            var durScale = 0.25; // Slightly faster for responsive feel
            var durPulse = 0.15;
            var baseDuration = 0.8; // Shorter base duration
            var totalShakeDuration = baseDuration + (this.shakeIntensity * 0.2); // Extend duration based on intensity

            var finishPulse = false;
            var finishShake = false;
            var finishScale = false;

            this.scaleLeaf = 0;
            this.boolAnimShake = true;

            // Clear any existing tweens
            this._clearActiveTweens();

            // Scale animation for new leaves with bounce effect
            this.tweenScale = this.tween({
                scaleLeaf: 1
            }, durScale, {
                easing: ig.Tween.Easing.Back.EaseOut,
                onComplete: function () {
                    finishScale = true;
                    this._checkAnimationComplete(finishPulse, finishShake, finishScale);
                }.bind(this)
            });
            this.tweenScale.start();
            this.activeTweens.push(this.tweenScale);

            // Subtle pulse animation
            this.tweenPulseOut = this.tween({
                alphaTree: 0.8
            }, durPulse, {
                easing: ig.Tween.Easing.Quadratic.EaseOut
            });

            this.tweenPulseIn = this.tween({
                alphaTree: 1
            }, durPulse, {
                easing: ig.Tween.Easing.Quadratic.EaseIn,
                onComplete: function () {
                    finishPulse = true;
                    this._checkAnimationComplete(finishPulse, finishShake, finishScale);
                }.bind(this)
            });

            this.tweenPulseOut.chain(this.tweenPulseIn);
            this.tweenPulseOut.start();
            this.activeTweens.push(this.tweenPulseOut);
            this.activeTweens.push(this.tweenPulseIn);

            // Pivot-based rotation shake animation with pendulum motion
            this._createPendulumShakeSequence(totalShakeDuration, function () {
                finishShake = true;
                this._checkAnimationComplete(finishPulse, finishShake, finishScale);
            }.bind(this));
        },

        _checkAnimationComplete: function (finishPulse, finishShake, finishScale) {
            if (finishPulse && finishShake && finishScale) {
                this.boolAnimShake = false;
                this.lastLeaves = this.totalLeaves;
                this.activeTweens = [];

                // Decay intensity
                this.shakeIntensity *= 0.7;
                this.pendingShakes = Math.max(0, this.pendingShakes - 1);

                // If there are pending shakes and sufficient intensity, restart animation
                if (this.pendingShakes > 0 && this.shakeIntensity > 0.1) {
                    // Small delay to prevent immediate restart feeling jarring
                    setTimeout(function () {
                        if (this.pendingShakes > 0) {
                            this._startShakeAnimation();
                        }
                    }.bind(this), 50);
                } else {
                    // Reset values when animation chain ends
                    this.shakeIntensity = 0;
                    this.pendingShakes = 0;
                }
            }
        },

        _clearActiveTweens: function () {
            for (var i = 0; i < this.activeTweens.length; i++) {
                if (this.activeTweens[i] && this.activeTweens[i].stop) {
                    this.activeTweens[i].stop();
                }
            }
            this.activeTweens = [];
        },

        _createPendulumShakeSequence: function (totalDuration, onComplete) {
            var shakeSteps = [];
            var currentTime = 0;
            var stepDuration = 0.08; // Slightly faster for responsive feel
            var baseAngleRadians = this.maxRotationDegrees * Math.PI / 180;
            var maxAngleRadians = baseAngleRadians * (1 + this.shakeIntensity); // Scale by intensity
            var frequency = 3 + (this.shakeIntensity * 1.5); // Increase frequency with intensity
            var dampingFactor = 0.88; // How quickly the pendulum motion dies down

            // Generate pendulum keyframes with natural damping
            while (currentTime < totalDuration) {
                var progress = currentTime / totalDuration;
                var dampedAmplitude = maxAngleRadians * Math.pow(dampingFactor, progress * 6);

                // Create pendulum motion (sinusoidal with decreasing amplitude)
                var angle = currentTime * frequency * Math.PI * 2;
                var rotationAngle = Math.sin(angle) * dampedAmplitude;

                // Add slight randomness for more organic feel (smaller than translation version)
                rotationAngle += (Math.random() - 0.5) * dampedAmplitude * 0.1;

                shakeSteps.push({
                    time: currentTime,
                    rotation: rotationAngle,
                    duration: stepDuration
                });

                currentTime += stepDuration;
            }

            // Add final return to center
            shakeSteps.push({
                time: totalDuration,
                rotation: 0,
                duration: 0.2
            });

            // Execute the pendulum sequence
            this._executePendulumSequence(shakeSteps, 0, onComplete);
        },

        _executePendulumSequence: function (shakeSteps, stepIndex, onComplete) {
            if (stepIndex >= shakeSteps.length) {
                if (onComplete) onComplete();
                return;
            }

            var step = shakeSteps[stepIndex];
            var isLastStep = stepIndex === shakeSteps.length - 1;

            // Choose easing based on step position - pendulum motion should be smooth
            var easing = isLastStep ?
                ig.Tween.Easing.Quadratic.EaseOut :
                ig.Tween.Easing.Sinusoidal.EaseInOut;

            this.tween({
                rotationAngle: step.rotation
            }, step.duration, {
                easing: easing,
                onComplete: function () {
                    this._executePendulumSequence(shakeSteps, stepIndex + 1, onComplete);
                }.bind(this)
            }).start();
        },

        drawTree: function () {

            // Draw grass (not affected by tree rotation)
            this.imgGrass.draw(ig.game.midX - this.imgGrass.width / 2 - 5, ig.system.height - this.imgGrass.height);

            // Calculate tree position with offset
            var treeX = this.pos.x + this.offsetTree.x - ig.game.screen.x;
            var treeY = this.pos.y + this.offsetTree.y - ig.game.screen.y;

            // Calculate pivot point (base of tree trunk)
            var pivotX = treeX + this.imgTree.width / 2;
            var pivotY = treeY + this.imgTree.height;

            // Apply rotation transformation around pivot point
            this.ctx.save();
            this.ctx.globalAlpha = this.alphaTree;

            // Translate to pivot point, rotate, then translate back
            this.ctx.translate(pivotX, pivotY);
            this.ctx.rotate(this.rotationAngle);
            this.ctx.translate(-pivotX, -pivotY);

            // Draw tree trunk
            this.imgTree.draw(treeX, treeY);

            // Draw leaves (they will inherit the rotation transformation)
            for (var leaf = 1; leaf <= this.totalLeaves; leaf++) {
                this.ctx.save();

                var typeL = this.typeLeaves[leaf];
                var posL = this.posLeaves[leaf];
                var flipL = this.flipLeaves[leaf];

                if (!posL) continue; // Skip if position data is missing

                this.ctx.translate(treeX + posL.x, treeY + posL.y);
                this.ctx.rotate(posL.angle * Math.PI / 180);

                if (leaf > this.lastLeaves) {
                    this.ctx.scale(flipL * this.scaleLeaf, this.scaleLeaf);
                } else {
                    this.ctx.scale(flipL, 1);
                }

                var leafImg = this['imgLeaf' + typeL];
                if (leafImg) {
                    if (typeL == 2 || typeL == 4) {
                        leafImg.draw((-leafImg.width / 2) - 4, -leafImg.height);
                    } else {
                        leafImg.draw(-leafImg.width / 2, -leafImg.height);
                    }
                }
                this.ctx.restore();
            }

            // Restore the main transformation
            this.ctx.restore();
        },

        draw: function () {
            this.parent();
            this.drawTree();
        },

        update: function () {
            this.parent();
            this.pos.x = ig.system.width * 0.5 + ig.game.screen.x - this.imgTree.width * 0.5;
            this.pos.y = ig.system.height + ig.game.screen.y - this.imgGrass.height - this.imgTree.height + 55;
        }
    });
});
