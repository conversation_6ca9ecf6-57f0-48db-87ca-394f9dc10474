ig.module('game.entities.controllers.controller-menu')
.requires(
    'impact.entity',
    'plugins.utils.entity-extended',
    'game.entities.buttons.button-play',
    'game.entities.buttons.button-settings',
    'plugins.utils.transition'
)
.defines(function () {
    "use strict";

    ig.ControllerMenu = ig.global.ControllerMenu = ig.EntityExtended.extend({
        zIndex: 1,
        name: 'controller-menu',
        buttons: {},

        titleImage: new ig.Image('media/graphics/sprites/ui/etc/title.png'),

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            ig.game.currentController = this;
            this.ctx = ig.system.context;

            this.transition = ig.game.spawnEntity(EntityTransition, 0, 0, { color: 'black' });

            ig.sizeHandler.reorient();
            ig.game.sortEntitiesDeferred();
        },

        ready: function () {
            this.transition.fadeOut(200, this.postInit.bind(this));
        },

        postInit: function () {
            this.setup();
            this.tweenIn();
            this.isReady = true;
        },

        setup: function () {
            this.titleImage.pos = { x: 0, y: 0 };
            this.titleImage._pos = { x: ig.sizeHandler.minW * 0.5 - this.titleImage.width * 0.5, y: 100 };
            this.titleImage.alpha = 0;
            this.titleImage.pos.x = this.titleImage._pos.x;
            this.titleImage.pos.y = this.titleImage._pos.y - 100;

            this.buttons = {};

            // === Setup Custom Functions
            this.buttons.updateAlpha = function (alpha) {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].updateAlpha === 'function') {
                        this.buttons[key].updateAlpha(alpha);
                    }
                }
            }.bind(this);

            this.buttons.disable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].disable === 'function') {
                        this.buttons[key].disable();
                    }
                }
            }.bind(this);

            this.buttons.enable = function () {
                for (var key in this.buttons) {
                    if (this.buttons[key] && typeof this.buttons[key].enable === 'function') {
                        this.buttons[key].enable();
                    }
                }
            }.bind(this);
            // === End Setup Custom Functions
            
            var padding = 40;
            this.buttons.play = this.spawnEntity(EntityButtonPlay, 0, 0);
            this.buttons.play.pos = {
                x: ig.sizeHandler.minW * 0.5 - this.buttons.play.size.x * 0.5,
                y: ig.sizeHandler.minH * 0.5 + this.buttons.play.size.y
            };

            this.buttons.settings = this.spawnEntity(EntityButtonSettings, 0, 0);
            this.buttons.settings.pos = {
                x: this.buttons.play.pos.x,
                y: this.buttons.play.pos.y + this.buttons.play.size.y + padding
            };
        },

        tweenIn: function () {
            var titleTween = new ig.TweenDef(this.titleImage)
                .to({ pos: { x: this.titleImage._pos.x, y: this.titleImage._pos.y }, alpha: 1 }, 800)
                .easing(ig.Tween.Easing.Cubic.EaseOut);
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 0 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 1 }, 800)
                .easing(ig.Tween.Easing.Cubic.EaseOut)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.enable();
                }.bind(this));
            
            titleTween.chain(buttonAlphaTween);
            titleTween.start();
        },

        tweenHide: function () {
            var titleTween = new ig.TweenDef(this.titleImage)
                .to({ alpha: 0 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .start();
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 1 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 0 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.disable();
                }.bind(this))
                .start();
        },

        tweenShow: function () {
            var titleTween = new ig.TweenDef(this.titleImage)
                .to({ alpha: 1 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .start();
            
            this.buttons.updateAlpha(0);
            this.buttons.disable();
            var alpha = { value: 0 };
            var buttonAlphaTween = new ig.TweenDef(alpha)
                .to({ value: 1 }, 100)
                .easing(ig.Tween.Easing.Cubic.EaseIn)
                .onUpdate(function () {
                    this.buttons.updateAlpha(alpha.value);
                }.bind(this))
                .onComplete(function () {
                    this.buttons.enable();
                }.bind(this))
                .start();
        },
        
        update: function () {
            this.parent();
        },
        
        draw: function () {
            this.parent();

            this.ctx.save();
            
            var grad = this.ctx.createLinearGradient(0, 0, 0, ig.system.height);
            grad.addColorStop(0, "#44B7E7"); 
            grad.addColorStop(1, "#E0F8FF"); 
            this.ctx.fillStyle = grad;
            this.ctx.fillRect(0, 0, ig.system.width, ig.system.height);

            this.ctx.restore();

            if (!this.isReady) return;
            this.ctx.save();
            this.ctx.translate(-ig.game.screen.x, -ig.game.screen.y);
            this.ctx.globalAlpha = this.titleImage.alpha;
            this.titleImage.draw(this.titleImage.pos.x, this.titleImage.pos.y);
            this.ctx.restore();
        }
    });
});
