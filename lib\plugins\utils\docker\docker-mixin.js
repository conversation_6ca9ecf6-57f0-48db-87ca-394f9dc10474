ig.module('plugins.utils.docker.docker-mixin')
.requires(
    'impact.entity'
)
.defines(function () {
    
    /**
     * Docker Mixin - Provides docking functionality to any entity
     * 
     * This mixin allows entities to dock to parent entities using:
     * - Percentage-based positioning (0.0 to 1.0)
     * - Pixel offsets for fine-tuning
     * 
     * Usage:
     * 1. Inject this mixin into your entity class
     * 2. Call dockTo() method or set dockerObject in settings
     */
    ig.DockerMixin = {
        
        // Docker properties
        _dockerParent: null,
        _dockerConfig: null,
        _isDocked: false,
        _dockerId: null,
        
        // Default docker configuration
        _defaultDockerConfig: {
            percent: { x: 0.5, y: 0.5 },  // Center by default
            offset: { x: 0, y: 0 },       // No offset by default
            isResponsive: false           // World coordinates by default
        },
        
        /**
         * Initialize docker functionality
         * Called automatically when entity is created with dockerObject in settings
         */
        initDocker: function(settings) {
            if (!settings) return;
            
            // Check if entity should be docked during initialization
            if (settings.dockerObject) {
                var dockerConfig = {
                    percent: settings.percent || this._defaultDockerConfig.percent,
                    offset: settings.offset || this._defaultDockerConfig.offset,
                    isResponsive: (typeof settings.isResponsive === 'boolean') ? settings.isResponsive : this._defaultDockerConfig.isResponsive
                };
                
                // Defer docking until next frame to ensure parent is fully initialized
                var self = this;
                setTimeout(function() {
                    self.dockTo(settings.dockerObject, dockerConfig);
                }, 0);
            }
        },
        
        /**
         * Dock this entity to a parent entity
         * @param {ig.Entity} parentEntity - The entity to dock to
         * @param {Object} config - Docking configuration
         * @param {Object} config.percent - Percentage positioning {x: 0-1, y: 0-1}
         * @param {Object} config.offset - Pixel offset {x: number, y: number}
         * @param {boolean} config.isResponsive - If true, position relative to screen instead of world
         */
        dockTo: function(parentEntity, config) {
            if (!parentEntity) {
                console.error('DockerMixin.dockTo: Parent entity is null or undefined');
                return false;
            }
            
            if (!parentEntity.size) {
                console.error('DockerMixin.dockTo: Parent entity has no size property');
                return false;
            }
            
            // Validate and normalize config
            config = this._validateDockerConfig(config);
            if (!config) {
                console.error('DockerMixin.dockTo: Invalid docker configuration');
                return false;
            }
            
            // Undock from previous parent if any
            this.undock();
            
            // Set up docking relationship
            this._dockerParent = parentEntity;
            this._dockerConfig = config;
            this._isDocked = true;
            this._dockerId = this._generateDockerId();
            
            // Register with docker manager
            if (ig.DockerManager) {
                ig.DockerManager.registerDockedEntity(this);
            }
            
            // Update position immediately
            this._updateDockedPosition();
            
            return true;
        },
        
        /**
         * Undock this entity from its parent
         */
        undock: function() {
            if (!this._isDocked) return;
            
            // Unregister from docker manager
            if (ig.DockerManager && this._dockerId) {
                ig.DockerManager.unregisterDockedEntity(this._dockerId);
            }
            
            // Clear docking properties
            this._dockerParent = null;
            this._dockerConfig = null;
            this._isDocked = false;
            this._dockerId = null;
        },
        
        /**
         * Update the docker configuration
         * @param {Object} newConfig - New docking configuration
         */
        updateDockerConfig: function(newConfig) {
            if (!this._isDocked) {
                console.warn('DockerMixin.updateDockerConfig: Entity is not docked');
                return false;
            }
            
            var validatedConfig = this._validateDockerConfig(newConfig);
            if (!validatedConfig) {
                console.error('DockerMixin.updateDockerConfig: Invalid docker configuration');
                return false;
            }
            
            this._dockerConfig = validatedConfig;
            this._updateDockedPosition();
            return true;
        },
        
        /**
         * Get current docker configuration
         * @returns {Object|null} Current docker config or null if not docked
         */
        getDockerConfig: function() {
            return this._isDocked ? ig.copy(this._dockerConfig) : null;
        },
        
        /**
         * Check if entity is currently docked
         * @returns {boolean} True if docked
         */
        isDocked: function() {
            return this._isDocked;
        },
        
        /**
         * Get the parent entity this is docked to
         * @returns {ig.Entity|null} Parent entity or null if not docked
         */
        getDockerParent: function() {
            return this._dockerParent;
        },

        /**
         * Update the docked position based on parent position and docker config
         * This is called automatically by the docker manager
         */
        _updateDockedPosition: function() {
            if (!this._isDocked || !this._dockerParent || !this._dockerConfig) {
                return;
            }

            // Check if parent entity is still alive
            if (this._dockerParent._killed) {
                this.undock();
                return;
            }

            var parent = this._dockerParent;
            var config = this._dockerConfig;

            // Calculate new position based on percentage and offset
            var newX = parent.pos.x + (parent.size.x * config.percent.x) + config.offset.x;
            var newY = parent.pos.y + (parent.size.y * config.percent.y) + config.offset.y;

            // If responsive, adjust for screen/camera position
            if (config.isResponsive && ig.game && ig.game.screen) {
                newX += ig.game.screen.x;
                newY += ig.game.screen.y;
            }

            // Update position
            this.pos.x = newX;
            this.pos.y = newY;
        },

        /**
         * Validate and normalize docker configuration
         * @param {Object} config - Configuration to validate
         * @returns {Object|null} Validated config or null if invalid
         */
        _validateDockerConfig: function(config) {
            if (!config) {
                return ig.copy(this._defaultDockerConfig);
            }

            var validatedConfig = {
                percent: { x: 0.5, y: 0.5 },
                offset: { x: 0, y: 0 },
                isResponsive: false
            };

            // Validate percent values
            if (config.percent) {
                if (typeof config.percent.x === 'number') {
                    validatedConfig.percent.x = Math.max(0, Math.min(1, config.percent.x));
                }
                if (typeof config.percent.y === 'number') {
                    validatedConfig.percent.y = Math.max(0, Math.min(1, config.percent.y));
                }
            }

            // Validate offset values
            if (config.offset) {
                if (typeof config.offset.x === 'number') {
                    validatedConfig.offset.x = config.offset.x;
                }
                if (typeof config.offset.y === 'number') {
                    validatedConfig.offset.y = config.offset.y;
                }
            }

            // Validate isResponsive value
            if (typeof config.isResponsive === 'boolean') {
                validatedConfig.isResponsive = config.isResponsive;
            }

            return validatedConfig;
        },

        /**
         * Generate a unique docker ID for this entity
         * @returns {string} Unique docker ID
         */
        _generateDockerId: function() {
            return 'docker_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        },

        /**
         * Override the kill method to handle undocking
         */
        kill: function() {
            this.undock();
            if (this.parent) {
                this.parent();
            }
        }
    };
});
