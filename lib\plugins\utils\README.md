# Utils Plugin - Modular Structure

The utils plugin has been refactored into a modular structure to enable selective importing and reduce bundle size. This allows developers to import only the specific utility functions they need.

## Structure

### Core Utilities
- `plugins.utils.core.types` - Type checking and basic type utilities
- `plugins.utils.core.array` - Array manipulation and search utilities  
- `plugins.utils.core.function` - Function utilities (throttle, debounce)

### Math Utilities
- `plugins.utils.math.basic` - Basic math functions (lerp, clamp, random, etc.)
- `plugins.utils.math.geometry` - Geometric calculations (distance, angles, polygons)
- `plugins.utils.math.curves` - Bezier curve generation

### Entity Utilities
- `plugins.utils.entity.types` - Entity type flag system
- `plugins.utils.entity.extensions` - Entity prototype extensions (delayedCall, Timer extensions)

### Game-Specific Utilities
- `plugins.utils.game.parking` - Parking slot geometry calculations

### Time Utilities
- `plugins.utils.time.format` - Time formatting functions

### Signal System
- `plugins.utils.signals.signal` - Complete signal/event system

### Logging
- `plugins.utils.logging.logger` - Colored console logging utilities

### Canvas Extensions
- `plugins.utils.canvas.extensions` - Canvas context extensions (roundRect)

## Usage

### Selective Importing
Import only what you need:

```javascript
ig.module('my-module')
.requires(
    'plugins.utils.math.basic',      // For lerp, clamp, random functions
    'plugins.utils.core.array'       // For array utilities
)
.defines(function() {
    // Use ig.utils.lerp, ig.utils.clamp, ig.utils.arrayCautiousAdd, etc.
});
```

### Full Compatibility (Backward Compatible)
For existing code, continue using the main module:

```javascript
ig.module('my-module')
.requires(
    'plugins.utils.utils'  // Loads all utilities via factory
)
.defines(function() {
    // All ig.utils.* functions available as before
});
```

### Factory Module
The `plugins.utils.utils-factory` module loads all utility modules and is used by the main `plugins.utils.utils` module for backward compatibility.

## Benefits

1. **Reduced Bundle Size**: Import only needed utilities
2. **Better Organization**: Logical grouping of related functions
3. **Selective Loading**: Faster loading for modules that only need specific utilities
4. **Backward Compatibility**: Existing code continues to work unchanged
5. **Maintainability**: Easier to maintain and extend specific utility categories

## Migration

No migration is required for existing code. The original `plugins.utils.utils` module continues to work exactly as before.

For new code, consider using selective imports to reduce bundle size:

```javascript
// Instead of requiring the full utils module:
.requires('plugins.utils.utils')

// Use specific modules:
.requires(
    'plugins.utils.math.basic',
    'plugins.utils.core.array'
)
```

## Available Functions by Module

### Core Types (`plugins.utils.core.types`)
- `ig.utils.type(o)` - Get object type
- `ig.utils.isNumber(n)` - Check if number
- `ig.utils.isArray(target)` - Check if array
- `ig.utils.toArray(target)` - Ensure array
- `ig.utils.toNotArray(target, index)` - Ensure not array

### Core Array (`plugins.utils.core.array`)
- `ig.utils.forEach(array, callback, args)` - Array iteration
- `ig.utils.indexOfValue(array, value)` - Find value index
- `ig.utils.indexOfProperty(array, property, value)` - Find by property
- `ig.utils.indexOfProperties(array, properties, values)` - Find by multiple properties
- `ig.utils.arrayCautiousAdd(target, element)` - Add if not exists
- `ig.utils.arrayCautiousAddMulti(target, elements)` - Add multiple if not exists
- `ig.utils.arrayCautiousRemove(target, element)` - Remove element
- `ig.utils.arrayCautiousRemoveMulti(target, elements)` - Remove multiple elements

### Core Function (`plugins.utils.core.function`)
- `ig.utils.throttle(callback, delay, trailing)` - Throttle function calls
- `ig.utils.debounce(callback, delay)` - Debounce function calls

### Math Basic (`plugins.utils.math.basic`)
- `ig.utils.lerp(value1, value2, amount)` - Linear interpolation
- `ig.utils.clamp(val, min, max)` - Clamp value
- `ig.utils.toRad(deg)` - Degrees to radians
- `ig.utils.toDeg(rad)` - Radians to degrees
- `ig.utils.randomBetween(a, b)` - Random float
- `ig.utils.randomBetweenInt(a, b)` - Random integer
- `ig.utils.pick(arr)` - Pick and remove random element
- `ig.utils.randomIn(arr)` - Pick random element (non-destructive)

### Math Geometry (`plugins.utils.math.geometry`)
- `ig.utils.distanceBetweenPoints(p1, p2)` - Distance between points
- `ig.utils.distanceBetween(x1, y1, x2, y2)` - Distance between coordinates
- `ig.utils.angleBetweenPoints(p1, p2)` - Angle between points
- `ig.utils.angleBetween(x1, y1, x2, y2)` - Angle between coordinates
- `ig.utils.rotateAround(obj, angle, distance)` - Rotate around point
- `ig.utils.rotatePoint(pointX, pointY, centerX, centerY, angle)` - Rotate point
- `ig.utils.getVertices(originX, originY, angle, width, height)` - Get rectangle vertices
- `ig.utils.containPoint(vertices, point)` - Point in polygon test
- `ig.utils.pointInPolygon(point, polygon)` - Point in polygon test (alternative)
- `ig.utils.calculatePolygonCenter(vertices)` - Calculate polygon center
- `ig.utils.distanceFromPointToLine(point, vertice1, vertice2)` - Point to line distance

And more functions in other modules...
