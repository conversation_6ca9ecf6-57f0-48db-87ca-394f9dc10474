ig.module('game.entities.controllers.controller-ads')
.requires(
	'impact.entity'
)
.defines(function () {
    ControllerAds = ig.Entity.extend({
        name: 'controller-ads',
        size: { x: 1, y: 1 },
        zIndex: 10,
        showAds: false,
        debugTextList: ['An ad is showing...', 'Ad Success', 'Ad Failed. Try again.'],
        debugText: null,
        rewardTime: 60,
        rewardType: null, // worker, booster, offline
        tweenOut: null,
        postSuccessCb: null,
        postFailCb: null,
        init: function (x, y, settings) {
            this.parent(x, y, settings);

            if (!ig.global.wm) {
                this.overlay = ig.game.spawnEntity(EntityPopupOverlay, 0, 0, { zIndex: this.zIndex - 1 });
                this.overlay.hide();
            }
            ig.game.sortEntitiesDeferred();
        },
        onAdsClicked: function (data) {
            this.debugText = this.debugTextList[0];
            this.doShowAds(this.onAdsSuccess.bind(this, data), this.onAdsFail.bind(this, data));
        },

        doShowAds: function (successCb, failCb) {
            // For testing purposes only
            this.showAds = true;
            this.overlay.show();
            // test success
            ig.game.timerManager.add(2, function () {
                successCb();
            }.bind(this, successCb));
            // test fail
            // ig.game.timerManager.add(2, function () {
            //     failCb();
            // }.bind(this, failCb));
        },

        onAdsSuccess: function (data) {
            // console.log('onAdsSuccess');
            this.debugText = this.debugTextList[1];
            ig.game.timerManager.add(2, function () {
                this.onAdRewarded(data);
                this.showAds = false;
            }.bind(this));
        },

        onAdsFail: function () {
            // console.log('onAdsFail');
            this.overlay.hide();
            this.debugText = this.debugTextList[2];
            ig.game.timerManager.add(3, function () {
                this.showAds = false;
            }.bind(this));
        },

        onAdRewarded: function (data) {
            this.overlay.hide();
            var rewardType = data.rewardType;
            switch (rewardType) {
                case 'field-prestige':
                    var popupUpgrade = ig.game.getEntityByName('popup-field-upgrade');
                    if (popupUpgrade) popupUpgrade.tweenOut(ig.controllers.idle.onFieldPrestige(data.fieldId));
                    break;
            }
        },

        update: function () {
            this.parent();
        },

        draw: function () {
            this.parent();
            if (!this.showAds) return;
            var ctx = ig.system.context;
            ctx.save();
            // create text
            ctx.fillStyle = '#ffffff';
            ctx.font = '90px montserrat';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(this.debugText, ig.system.width * 0.5, ig.system.height * 0.5);
            ctx.restore();
        }
    });

    /* EntityPopupOverlay = ig.Entity.extend({
        name: 'popup-overlay',
        size: { x: 0, y: 0 },
        zIndex: 999,
        alpha: 0,

        init: function (x, y, settings) {
            this.parent(0, 0, settings);
            this._zIndex = settings.zIndex;
            this.size = { x: ig.system.width, y: ig.system.height };
            this.tweenIn();
        },

        tweenIn: function () {
            new ig.TweenDef(this)
                .to({ alpha: 0.4 }, 200)
                .start();
        },

        tweenOut: function (callback) {
            new ig.TweenDef(this)
                .to({ alpha: 0 }, 200)
                .onComplete(function () {
                    if (typeof callback === 'function') {
                        callback();
                    }
                    this.kill();
                }.bind(this))
                .start();
        },

        show: function () {
            this.alpha = 0.4;
            this.zIndex = this._zIndex;
            ig.game.sortEntitiesDeferred();
        },

        hide: function () {
            this.alpha = 0;
            this._zIndex = this.zIndex;
            this.zIndex = -1;
            ig.game.sortEntitiesDeferred();
        },

        draw: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.fillStyle = 'rgba(0, 0, 0, ' + this.alpha + ')';
            ctx.fillRect(0, 0, this.size.x, this.size.y);
            ctx.restore();
        },

        update: function () {
            // Ensure the overlay always covers the screen
            this.pos.x = ig.game.screen.x;
            this.pos.y = ig.game.screen.y;
            this.size.x = ig.system.width;
            this.size.y = ig.system.height;
        }
    }); */
});
