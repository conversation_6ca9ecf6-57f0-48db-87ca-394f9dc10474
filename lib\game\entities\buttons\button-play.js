ig.module('game.entities.buttons.button-play')
.requires(
    'plugins.utils.buttons.button-image'
)
.defines(function () {
    "use strict";

    ig.EntityButtonPlay = ig.global.EntityButtonPlay = EntityButtonImage.extend({
        name: 'button-play',
		idleSheetInfo: { sheetImage: new ig.Image('media/graphics/sprites/ui/button/button-play.png'), frameCountX: 1, frameCountY: 1 },
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.idle = new ig.Animation(this.idleSheet, 1, [0], true);
            this.currentAnim = this.idle;
            ig.game.sortEntitiesDeferred();
        },
        
        onClickCallback: function () {
            this._parent.transition.fadeIn(500, function () {
                ig.game.director.jumpTo(LevelGame);
            });
        }
    });
});
