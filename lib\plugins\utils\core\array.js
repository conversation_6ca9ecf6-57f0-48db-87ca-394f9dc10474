ig.module('plugins.utils.core.array')
.requires(
    'plugins.utils.core.types'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    ig.utils.forEach = function (array, callback, args) {
        for (var i = 0, il = array.length; i < il; i++) {
            callback.apply(array[i], args);
        }
    };

    ig.utils.indexOfValue = function (array, value) {
        for (var i = 0, il = array.length; i < il; i++) {
            if (value === array[i]) { return i; }
        }
        return -1;
    };

    ig.utils.indexOfProperty = function (array, property, value) {
        for (var i = 0, il = array.length; i < il; i++) {
            if (value === array[i][property]) { return i; }
        }
        return -1;
    };

    ig.utils.indexOfProperties = function (array, properties, values) {
        for (var i = 0, il = array.length; i < il; i++) {
            var obj = array[i];
            var missing = false;
            for (var j = 0, jl = properties.length; j < jl; j++) {
                if (values[j] !== obj[properties[j]]) { missing = true; break; }
            }
            if (!missing) { return i; }
        }
        return -1;
    };

    ig.utils.arrayCautiousAdd = function (target, element) {
        var index = ig.utils.indexOfValue(target, element);
        if (index === -1) { target.push(element); }
        return target;
    };

    ig.utils.arrayCautiousAddMulti = function (target, elements) {
        var element, index; elements = ig.utils.toArray(elements);
        for (var i = 0, il = elements.length; i < il; i++) {
            element = elements[i];
            if (element !== target) {
                index = ig.utils.indexOfValue(target, element);
                if (index === -1) { target.push(element); }
            }
        }
        return target;
    };

    ig.utils.arrayCautiousRemove = function (target, element) {
        var index = ig.utils.indexOfValue(target, element);
        if (index !== -1) { target.splice(index, 1); }
        return target;
    };

    ig.utils.arrayCautiousRemoveMulti = function (target, elements) {
        var element, index; elements = ig.utils.toArray(elements);
        for (var i = 0, il = elements.length; i < il; i++) {
            element = elements[i];
            if (element !== target) {
                index = ig.utils.indexOfValue(target, element);
                if (index !== -1) { target.splice(index, 1); }
            }
        }
        return target;
    };
});

