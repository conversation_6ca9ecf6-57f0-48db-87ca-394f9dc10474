ig.module('plugins.utils.canvas.extensions')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    /**
     * Adds a method to CanvasRenderingContext2D to draw rounded rectangles.
     * @param {Number} x X-coordinate
     * @param {Number} y Y-coordinate
     * @param {Number} width Width of the rectangle
     * @param {Number} height Height of the rectangle
     * @param {Number} [radius=5] Radius of the corners
     */
    CanvasRenderingContext2D.prototype.roundRect = function (x, y, width, height, radius) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }
        this.beginPath();
        this.moveTo(x + radius, y);
        this.lineTo(x + width - radius, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.lineTo(x + width, y + height - radius);
        this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.lineTo(x + radius, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.lineTo(x, y + radius);
        this.quadraticCurveTo(x, y, x + radius, y);
        this.closePath();
        this.fill();
    };
});
