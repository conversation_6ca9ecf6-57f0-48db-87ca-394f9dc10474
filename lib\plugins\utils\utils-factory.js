ig.module('plugins.utils.utils-factory')
.requires(
    // Core utilities (load first as they may be dependencies)
    'plugins.utils.core.types',
    'plugins.utils.core.array',
    'plugins.utils.core.function',

    // Math utilities
    'plugins.utils.math.basic',
    'plugins.utils.math.geometry',
    'plugins.utils.math.curves',

    // Entity utilities
    'plugins.utils.entity.types',
    'plugins.utils.entity.extensions',

    // Time utilities
    'plugins.utils.time.format',

    // Signal system
    'plugins.utils.signals.signal',

    // Logging utilities
    'plugins.utils.logging.logger',

    // Canvas extensions
    'plugins.utils.canvas.extensions'
)
.defines(function () {
    /* Factory module to require all utility modules for backward compatibility */

    // Debug: Log that factory has loaded
    console.log('Utils factory loaded. Available functions:');
    var count = 0;
    for (var prop in ig.utils) {
        if (typeof ig.utils[prop] === 'function') {
            count++;
        }
    }
    console.log('Total utility functions loaded:', count);
});
