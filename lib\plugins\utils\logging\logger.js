ig.module('plugins.utils.logging.logger')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    /**
     * Logging Utilities
     */
    ig.rlog = {
        debug: window.console.log.bind(window.console, '%c[DEBUG]', 'color: #5393e2;'),
        error: window.console.log.bind(window.console, '%c[ERROR]', 'color: #ee9698;'),
        warn: window.console.log.bind(window.console, '%c[WARN]', 'color: #f9ffb4;'),
        info: window.console.log.bind(window.console, '%c[INFO]', 'color: #d6f6ff;')
    };
});
