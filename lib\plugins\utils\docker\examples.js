ig.module('plugins.utils.docker.examples')
.requires(
    'plugins.utils.docker.docker',
    'plugins.utils.entity-extended'
)
.defines(function () {

    // Set up input bindings for docker demo
    if (ig.input) {
        ig.input.bind(ig.KEY.SPACE, 'move-parent');
        ig.input.bind(ig.KEY.ENTER, 'rotate-demo');
    }
    
    /**
     * Docker Plugin Usage Examples
     * 
     * This file contains example entities demonstrating how to use the docker plugin
     * with both usage methods.
     */
    
    // Example parent entity that can have children docked to it
    EntityDockerParent = ig.EntityExtended.extend({
        size: { x: 100, y: 100 },
        zIndex: 1,
        
        // Visual properties
        color: '#4CAF50',
        borderColor: '#2E7D32',
        
        init: function(x, y, settings) {
            this.parent(x, y, settings);
            
            // Example: Create docked children using Method 2 (spawn with dockerObject)
            this.createDockedChildren();
        },
        
        createDockedChildren: function() {
            // Create a child docked to the top-left corner
            ig.game.spawnEntity(EntityDockerChild, 0, 0, {
                dockerObject: this,
                percent: { x: 0, y: 0 },
                offset: { x: -10, y: -10 },
                color: '#FF5722',
                label: 'Top-Left'
            });
            
            // Create a child docked to the center
            ig.game.spawnEntity(EntityDockerChild, 0, 0, {
                dockerObject: this,
                percent: { x: 0.5, y: 0.5 },
                offset: { x: 0, y: 0 },
                color: '#2196F3',
                label: 'Center'
            });
            
            // Create a child docked to the bottom-right corner
            ig.game.spawnEntity(EntityDockerChild, 0, 0, {
                dockerObject: this,
                percent: { x: 1, y: 1 },
                offset: { x: 10, y: 10 },
                color: '#9C27B0',
                label: 'Bottom-Right'
            });

            // Create a responsive child that stays relative to screen
            ig.game.spawnEntity(EntityDockerChild, 0, 0, {
                dockerObject: this,
                percent: { x: 0, y: 0.5 },
                offset: { x: -30, y: 0 },
                isResponsive: true,
                color: '#FF9800',
                label: 'Responsive'
            });
        },
        
        update: function() {
            this.parent();
            
            // Example: Move the parent around to demonstrate docking
            if (ig.input.state('move-parent')) {
                this.pos.x += 50 * ig.system.tick;
                this.pos.y += 30 * ig.system.tick;
                
                // Wrap around screen
                if (this.pos.x > ig.system.width) this.pos.x = -this.size.x;
                if (this.pos.y > ig.system.height) this.pos.y = -this.size.y;
            }
        },
        
        draw: function() {
            var ctx = ig.system.context;
            var x = ig.system.getDrawPos(this.pos.x - ig.game.screen.x);
            var y = ig.system.getDrawPos(this.pos.y - ig.game.screen.y);
            
            ctx.save();
            
            // Draw parent entity
            ctx.fillStyle = this.color;
            ctx.fillRect(x, y, this.size.x, this.size.y);
            
            // Draw border
            ctx.strokeStyle = this.borderColor;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, this.size.x, this.size.y);
            
            // Draw label
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Parent', x + this.size.x / 2, y + this.size.y / 2 + 4);
            
            ctx.restore();
        }
    });
    
    // Example child entity that can be docked to parents
    EntityDockerChild = ig.EntityExtended.extend({
        size: { x: 20, y: 20 },
        zIndex: 2,
        
        // Visual properties
        color: '#FFC107',
        borderColor: '#F57F17',
        label: 'Child',
        
        init: function(x, y, settings) {
            this.parent(x, y, settings);
            
            // Apply settings
            if (settings) {
                if (settings.color) this.color = settings.color;
                if (settings.label) this.label = settings.label;
            }
        },
        
        draw: function() {
            var ctx = ig.system.context;
            var x = ig.system.getDrawPos(this.pos.x - ig.game.screen.x);
            var y = ig.system.getDrawPos(this.pos.y - ig.game.screen.y);
            
            ctx.save();
            
            // Draw child entity
            ctx.fillStyle = this.color;
            ctx.fillRect(x, y, this.size.x, this.size.y);
            
            // Draw border
            ctx.strokeStyle = this.borderColor;
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, this.size.x, this.size.y);
            
            // Draw label above the child
            ctx.fillStyle = '#000000';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.label, x + this.size.x / 2, y - 5);
            
            ctx.restore();
        }
    });
    
    // Example entity demonstrating Method 1 (dockTo method)
    EntityDockerDemo = ig.EntityExtended.extend({
        size: { x: 80, y: 80 },
        zIndex: 1,
        
        color: '#E91E63',
        borderColor: '#AD1457',
        
        init: function(x, y, settings) {
            this.parent(x, y, settings);
            
            // Example: Create a child and dock it using Method 1
            this.createAndDockChild();
        },
        
        createAndDockChild: function() {
            // Create a child entity first
            var child = ig.game.spawnEntity(EntityDockerChild, this.pos.x, this.pos.y, {
                color: '#00BCD4',
                label: 'Method 1'
            });
            
            // Then dock it using Method 1 (dockTo method)
            child.dockTo(this, {
                percent: { x: 0.5, y: 0 },  // Top center
                offset: { x: 0, y: -25 }    // 25 pixels above
            });
        },
        
        update: function() {
            this.parent();
            
            // Example: Rotate around a point to demonstrate docking
            if (ig.input.state('rotate-demo')) {
                var centerX = ig.system.width / 2;
                var centerY = ig.system.height / 2;
                var radius = 100;
                var time = ig.Timer.timeScale * 2;
                
                this.pos.x = centerX + Math.cos(time) * radius - this.size.x / 2;
                this.pos.y = centerY + Math.sin(time) * radius - this.size.y / 2;
            }
        },
        
        draw: function() {
            var ctx = ig.system.context;
            var x = ig.system.getDrawPos(this.pos.x - ig.game.screen.x);
            var y = ig.system.getDrawPos(this.pos.y - ig.game.screen.y);
            
            ctx.save();
            
            // Draw demo entity
            ctx.fillStyle = this.color;
            ctx.fillRect(x, y, this.size.x, this.size.y);
            
            // Draw border
            ctx.strokeStyle = this.borderColor;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, this.size.x, this.size.y);
            
            // Draw label
            ctx.fillStyle = '#FFFFFF';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Demo', x + this.size.x / 2, y + this.size.y / 2 + 3);
            
            ctx.restore();
        }
    });
    
    // Utility function to create a docker demonstration scene
    ig.Docker.createDemoScene = function() {
        // Create parent entity
        var parent = ig.game.spawnEntity(EntityDockerParent, 200, 150);
        
        // Create demo entity
        var demo = ig.game.spawnEntity(EntityDockerDemo, 400, 200);
        
        console.log('Docker Demo Scene: Created');
        console.log('Instructions:');
        console.log('- Hold SPACE to move parent entity');
        console.log('- Hold ENTER to rotate demo entity');
        
        return { parent: parent, demo: demo };
    };
});
