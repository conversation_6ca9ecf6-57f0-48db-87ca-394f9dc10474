# Docker Plugin for ImpactJS

A reusable component that allows entities to dock to parent entities using percentage-based positioning and pixel offsets.

## Features

- **Percentage-based positioning**: Position child entities relative to parent using values from 0.0 to 1.0
- **Pixel offset support**: Fine-tune positioning with pixel-perfect offsets
- **Automatic updates**: Child entities automatically follow parent movement
- **Automatic cleanup**: Docking relationships are cleaned up when parent entities are destroyed
- **Two usage methods**: Direct method calls or spawn-time configuration
- **Manager system**: Centralized management of all docking relationships

## Installation

1. Include the docker plugin in your main.js requires:

```javascript
ig.module('game.main')
.requires(
    // ... other requires
    'plugins.utils.docker.docker'
)
```

2. The plugin automatically injects docker functionality into all entities.

## Usage

### Method 1: Direct docking with dockTo()

```javascript
// Create parent entity
var parent = ig.game.spawnEntity(EntityParent, 100, 100);

// Create child entity
var child = ig.game.spawnEntity(EntityChild, 0, 0);

// Dock child to parent
child.dockTo(parent, {
    percent: { x: 0.5, y: 0.5 },  // Center of parent
    offset: { x: 10, y: 10 },     // 10 pixels right and down
    isResponsive: false           // World coordinates (default)
});
```

### Method 2: Spawn with docker configuration

```javascript
// Create parent entity
var parent = ig.game.spawnEntity(EntityParent, 100, 100);

// Spawn child already docked to parent
var child = ig.game.spawnEntity(EntityChild, 0, 0, {
    dockerObject: parent,
    percent: { x: 0.5, y: 0.5 },
    offset: { x: 10, y: 10 },
    isResponsive: false
});
```

## Configuration

### Percent Values
- `x`: 0.0 = left edge, 0.5 = center, 1.0 = right edge
- `y`: 0.0 = top edge, 0.5 = center, 1.0 = bottom edge

### Offset Values
- `x`: Pixel offset horizontally (positive = right, negative = left)
- `y`: Pixel offset vertically (positive = down, negative = up)

### Responsive Mode
- `isResponsive`: Boolean flag (default: false)
  - `false`: Position relative to world coordinates (default behavior)
  - `true`: Position relative to screen/camera coordinates (adds/subtracts ig.game.screen.x/y)

## Position Calculation

The final position is calculated as:
```
childX = parentX + (parentWidth * percent.x) + offset.x
childY = parentY + (parentHeight * percent.y) + offset.y
```

## API Reference

### Entity Methods (available on all entities)

#### `dockTo(parentEntity, config)`
Dock this entity to a parent entity.
- `parentEntity`: The entity to dock to
- `config`: Configuration object with `percent` and `offset` properties
- Returns: `boolean` - true if successful

#### `undock()`
Undock this entity from its parent.

#### `isDocked()`
Check if entity is currently docked.
- Returns: `boolean`

#### `getDockerParent()`
Get the parent entity this is docked to.
- Returns: `ig.Entity|null`

#### `getDockerConfig()`
Get current docker configuration.
- Returns: `Object|null`

#### `updateDockerConfig(newConfig)`
Update the docker configuration.
- `newConfig`: New configuration object
- Returns: `boolean` - true if successful

### Utility Functions

#### `ig.Docker.createDockedEntity(entityClass, parentEntity, dockerConfig, spawnSettings)`
Create and dock an entity in one call.

#### `ig.Docker.dockEntity(childEntity, parentEntity, dockerConfig)`
Dock an existing entity to a parent.

#### `ig.Docker.undockEntity(entity)`
Undock an entity.

#### `ig.Docker.getDockedEntities(parentEntity)`
Get all entities docked to a parent.

#### `ig.Docker.undockAllFrom(parentEntity)`
Undock all entities from a parent.

#### `ig.Docker.getStats()`
Get docker system statistics.

### Manager Functions

#### `ig.DockerManager.getDockedEntityCount()`
Get the total number of docked entities.

#### `ig.DockerManager.getAllDockedEntities()`
Get array of all docked entities.

#### `ig.DockerManager.getEntitiesDockedTo(parentEntity)`
Get entities docked to a specific parent.

#### `ig.DockerManager.undockAllFrom(parentEntity)`
Undock all entities from a parent.

## Examples

### Basic UI Element Docking
```javascript
// Create a button
var button = ig.game.spawnEntity(EntityButton, 100, 100);

// Create a label docked to the button's center
var label = ig.game.spawnEntity(EntityText, 0, 0, {
    dockerObject: button,
    percent: { x: 0.5, y: 0.5 },
    offset: { x: 0, y: 0 }
});
```

### Health Bar Above Character
```javascript
// Create character
var character = ig.game.spawnEntity(EntityPlayer, 200, 200);

// Create health bar above character
var healthBar = ig.game.spawnEntity(EntityHealthBar, 0, 0, {
    dockerObject: character,
    percent: { x: 0.5, y: 0 },    // Top center
    offset: { x: 0, y: -20 }      // 20 pixels above
});
```

### Corner Indicators
```javascript
// Create main entity
var mainEntity = ig.game.spawnEntity(EntityMain, 150, 150);

// Top-left indicator
ig.game.spawnEntity(EntityIndicator, 0, 0, {
    dockerObject: mainEntity,
    percent: { x: 0, y: 0 },
    offset: { x: -5, y: -5 }
});

// Bottom-right indicator
ig.game.spawnEntity(EntityIndicator, 0, 0, {
    dockerObject: mainEntity,
    percent: { x: 1, y: 1 },
    offset: { x: 5, y: 5 }
});
```

### Responsive UI Elements
```javascript
// Create a UI panel that stays in screen coordinates
var uiPanel = ig.game.spawnEntity(EntityUIPanel, 100, 50);

// Create a button docked to the panel that moves with the camera
var button = ig.game.spawnEntity(EntityButton, 0, 0, {
    dockerObject: uiPanel,
    percent: { x: 0.5, y: 0.8 },
    offset: { x: 0, y: 0 },
    isResponsive: true  // This button will stay relative to screen
});

// When the camera moves, the button maintains its screen position
// relative to the panel, rather than world position
```

### Mixed Coordinate Systems
```javascript
// World-space entity (moves with camera)
var worldEntity = ig.game.spawnEntity(EntityWorld, 200, 200);

// Screen-space UI element
var screenEntity = ig.game.spawnEntity(EntityUI, 50, 50);

// Dock world element to screen element with responsive positioning
worldEntity.dockTo(screenEntity, {
    percent: { x: 1, y: 0 },
    offset: { x: 10, y: 0 },
    isResponsive: true  // World entity now follows screen coordinates
});
```

## Testing

To test the docker plugin functionality:

```javascript
// Run automated tests
ig.Docker.runTests();

// Create visual test scene
ig.Docker.createTestScene();
```

## Notes

- The docker plugin automatically handles entity cleanup when parents are destroyed
- Docked entities are updated every frame to maintain their relative positions
- The plugin is compatible with the existing entity system and doesn't interfere with other functionality
- All percentage values are automatically clamped to the 0.0-1.0 range
- The plugin works with any entity that has `pos` and `size` properties
