ig.module('plugins.utils.math.basic')
.requires(
    'impact.impact'
)
.defines(function () { 'use strict';

    ig.utils = ig.utils || {};

    // Math constants
    ig.utils.pi2 = Math.PI * 2;
    ig.utils.pio2 = Math.PI / 2;

    /**
     * Clamps a value between a minimum and maximum.
     * @param {Number} val Value to clamp
     * @param {Number} min Minimum value
     * @param {Number} max Maximum value
     * @returns {Number} Clamped value
     */
    ig.utils.clamp = function (val, min, max) {
        return Math.max(min, Math.min(max, val));
    };

    /**
     * Linearly interpolates between two values.
     * @param {Number} value1 Start value
     * @param {Number} value2 End value
     * @param {Number} amount Interpolation amount [0, 1]
     * @returns {Number} Interpolated value
     */
    ig.utils.lerp = function (value1, value2, amount) {
        amount = ig.utils.clamp(amount, 0, 1);
        return value1 + (value2 - value1) * amount;
    };

    /**
     * Converts degrees to radians.
     * @param {Number} deg Angle in degrees
     * @returns {Number} Angle in radians
     */
    ig.utils.toRad = function (deg) {
        return deg / 180 * Math.PI;
    };

    /**
     * Converts radians to degrees.
     * @param {Number} rad Angle in radians
     * @returns {Number} Angle in degrees
     */
    ig.utils.toDeg = function (rad) {
        return rad / Math.PI * 180;
    };

    /**
     * Generates a random float between two values.
     * @param {Number} a Minimum value
     * @param {Number} b Maximum value
     * @returns {Number} Random float between a and b
     */
    ig.utils.randomBetween = function (a, b) {
        return a + Math.random() * (b - a);
    };

    /**
     * Generates a random integer between two values (inclusive).
     * @param {Number} a Minimum integer
     * @param {Number} b Maximum integer
     * @returns {Number} Random integer between a and b
     */
    ig.utils.randomBetweenInt = function (a, b) {
        return a + Math.floor(Math.random() * (b - a + 1));
    };

    /**
     * Picks and removes a random element from an array.
     * @param {Array} arr Array to pick from
     * @returns {*} Removed element
     */
    ig.utils.pick = function (arr) {
        var r = Math.floor(Math.random() * arr.length);
        return arr.splice(r, 1)[0];
    };

    /**
     * Picks a random element from an array without removing it.
     * @param {Array} arr Array to pick from
     * @returns {*} Random element
     */
    ig.utils.randomIn = function (arr) {
        var r = Math.floor(Math.random() * arr.length);
        return arr[r];
    };
});
